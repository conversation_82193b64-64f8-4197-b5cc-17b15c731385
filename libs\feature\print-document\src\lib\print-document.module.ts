import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { PrintDocumentComponent } from './components/print-document/print-document.component'
import { PrintImageSourceComponent } from './components/print-image-source/print-image-source.component'
import { PrintSlipsheetOptionComponent } from './components/print-slipsheet-option/print-slipsheet-option.component'
import { PrintEndorsementOptionComponent } from './components/print-endorsement-option/print-endorsement-option.component'
import { PrintWatermarkOptionComponent } from './components/print-watermark-option/print-watermark-option.component'
import { PrintOutputOptionComponent } from './components/print-output-option/print-output-option.component'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import {
  ColorPickerModule,
  TextAreaModule,
  TextBoxModule,
  InputsModule,
} from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { ButtonModule, ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  ExpansionPanelModule,
  TabStripModule,
} from '@progress/kendo-angular-layout'
import {
  DropDownListModule,
  DropDownsModule,
} from '@progress/kendo-angular-dropdowns'
import { PrintAddFieldComponent } from './components/print-add-field/print-add-field.component'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { PrintSummaryComponent } from './components/print-summary/print-summary.component'
import { PrintDocumentFormService } from './services/print-document-form.service'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { DocumentPrintDownloadComponent } from './components/document-print-download-dialog/document-print-download.component'
import { DocumentPrintDownloadStatusComponent } from './components/document-print-download-status/document-print-download-status.component'
import { PrintResizeImageComponent } from './components/print-resize-image/print-resize-image.component'

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TextBoxModule,
    TextAreaModule,
    LabelModule,
    GridModule,
    InputsModule,
    ButtonModule,
    ColorPickerModule,
    DropDownsModule,
    DialogModule,
    TooltipsModule,
    ButtonsModule,
    SvgLoaderDirective,
    TabStripModule,
    UserGroupRightCheckDirective,
    PrintResizeImageComponent,
    ExpansionPanelModule,
    DynamicHeightDirective,
    DropDownListModule,
  ],
  declarations: [
    PrintDocumentComponent,
    PrintImageSourceComponent,
    PrintSlipsheetOptionComponent,
    PrintEndorsementOptionComponent,
    PrintWatermarkOptionComponent,
    PrintOutputOptionComponent,
    PrintAddFieldComponent,
    PrintSummaryComponent,
    DocumentPrintDownloadComponent,
    DocumentPrintDownloadStatusComponent,
  ],
  providers: [PrintDocumentFormService],
})
export class PrintDocumentModule {}
