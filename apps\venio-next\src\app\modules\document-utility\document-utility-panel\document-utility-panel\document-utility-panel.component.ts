import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  Type,
  ViewChild,
  ViewContainerRef,
  inject,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DropDownsModule,
  RemoveTagEvent,
} from '@progress/kendo-angular-dropdowns'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import {
  ExpansionPanelActionEvent,
  ExpansionPanelModule,
} from '@progress/kendo-angular-layout'
import {
  DragOverEvent,
  DragStartEvent,
  SortableComponent,
  SortableModule,
} from '@progress/kendo-angular-sortable'
import {
  UtilityPanel,
  UtilityPanelFacade,
  UtilityPanelItem,
  UtilityPanelPermission,
} from '@venio/data-access/document-utility'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { MetadataCodingHistoryModule } from '../../metadata-coding-history/metadata-coding-history.module'
import { TagCodingModule } from '../../tag-coding/tag-coding.module'
import { VerticalToolbarModule } from '../../vertical-toolbar/vertical-toolbar.module'
import {
  componentIdMapping,
  ExcludedPanelsFromRight,
  titleMapping,
  UtilityPanelTitle,
  UtilityPanelType,
} from '@venio/shared/models/constants'
import {
  Subject,
  Subscription,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil,
} from 'rxjs'
import { LocalStorage } from '@venio/shared/storage'
import { cloneDeep, isEqual } from 'lodash'
import { DebounceTimer } from '@venio/util/utilities'
import {
  CompositeLayoutState,
  FieldFacade,
  LayoutPanel,
  StartupsFacade,
  UserRights,
} from '@venio/data-access/review'
import { NotificationTriggerService } from '../../../../services/shared-custom.notification.service'
import {
  NotificationService,
  Type as NotificationType,
} from '@progress/kendo-angular-notification'
import { IconsModule } from '@progress/kendo-angular-icons'
import { eyeIcon } from '@progress/kendo-svg-icons'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { CellTemplateDirective } from '@progress/kendo-angular-grid'

@Component({
  selector: 'venio-document-utility-panel',
  standalone: true,
  imports: [
    CommonModule,
    SvgLoaderDirective,
    ButtonsModule,
    IndicatorsModule,
    DropDownsModule,
    SortableModule,
    ExpansionPanelModule,
    FormsModule,
    VerticalToolbarModule,
    TagCodingModule,
    MetadataCodingHistoryModule,
    IconsModule,
    TooltipDirective,
    CellTemplateDirective,
  ],
  templateUrl: './document-utility-panel.component.html',
  styleUrl: './document-utility-panel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentUtilityPanelComponent implements OnInit, OnDestroy {
  public verticalToolbarContainerComponent = import(
    '../../vertical-toolbar/vertical-toolbar-container/vertical-toolbar-container.component'
  ).then(
    ({ VerticalToolbarContainerComponent }) => VerticalToolbarContainerComponent
  )

  public actionbarContainerComponent = import(
    '../../document-utility-action-bar/document-utility-action-bar.component'
  ).then(
    ({ DocumentUtilityActionBarComponent }) => DocumentUtilityActionBarComponent
  )

  public metadataComponent = import(
    '../../metadata-coding-history/metadata/metadata.component'
  ).then(({ MetadataComponent }) => MetadataComponent)

  public notesComponent = import('@venio/feature/document-notes').then(
    ({ DocumentNotesComponent }) => DocumentNotesComponent
  )

  public tagCodingContainerComponent = import(
    '../../tag-coding/tag-coding-container/tag-coding-container.component'
  ).then(({ TagCodingContainerComponent }) => TagCodingContainerComponent)

  public parentChildComponent = import(
    '../../parent-child/parent-child.component'
  ).then(({ ParentChildComponent }) => ParentChildComponent)

  public duplicateComponent = import(
    '../../document-duplicates/document-duplicates.component'
  ).then(({ DocumentDuplicatesComponent }) => DocumentDuplicatesComponent)

  public documentHistoryComponent = import(
    '../../document-history/document-history.component'
  ).then(({ DocumentHistoryComponent }) => DocumentHistoryComponent)

  public documentSimilarComponent = import(
    '../../document-similar/document-similar.component'
  ).then(({ DocumentSimilarComponent }) => DocumentSimilarComponent)

  public emailThreadComponent = import('@venio/feature/email-thread').then(
    ({ EmailThreadComponent }) => EmailThreadComponent
  )

  public nearDuplicateComponent = import(
    '../../document-near-duplicates/document-near-duplicates.component'
  ).then(
    ({ DocumentNearDuplicatesComponent }) => DocumentNearDuplicatesComponent
  )

  //Container placeholder for filter panel
  public documentUtilityFilterPanelComponent = import(
    '../../document-utility-filter-panel/document-utility-filter-panel-container/document-utility-filter-panel-container.component'
  ).then((m) => m.DocumentUtilityFilterPanelContainerComponent)

  public edaiDocumentRelevancyComponent = import(
    '../../../edai/edai-document-relevancy-container/edai-document-relevancy-container.component'
  ).then(
    ({ EdaiDocumentRelevancyContainerComponent }) =>
      EdaiDocumentRelevancyContainerComponent
  )

  public edaiDocumentPrivilegeComponent = import(
    '../../../edai/edai-document-privilege-container/edai-document-privilege-container.component'
  ).then(
    ({ EdaiDocumentPrivilegeContainerComponent }) =>
      EdaiDocumentPrivilegeContainerComponent
  )

  public edaiDocumentPIIComponent = import(
    '../../../edai/edai-document-pii-container/edai-document-pii-container.component'
  ).then(
    ({ EdaiDocumentPiiContainerComponent }) => EdaiDocumentPiiContainerComponent
  )

  @ViewChild(SortableComponent)
  public sortable: SortableComponent

  @ViewChild('appendNotification', { read: ViewContainerRef, static: false })
  public appendTo: ViewContainerRef

  public tagCount = 0

  private initialUtilityPanelItems: UtilityPanelItem[] = [] //= UtilityPanelItems

  public utilityPanelItems = signal<UtilityPanelItem[]>([])

  public utilityPanelList = signal<string[]>([])

  public filteredList = signal<string[]>([])

  public selectedUtilityPanels = signal<string[]>([])

  public utilityPanelTitle = UtilityPanelTitle

  public readonly utilityPanelType = UtilityPanelType

  public visibilityStatus: { [key: string]: boolean } = {}

  public expandedStatus: { [key: string]: boolean } = {}

  private panelSortOrder: string[] = []

  private isPanelSorted = false

  public viewerInFullScreen = false

  private toDestroy$: Subject<void> = new Subject<void>()

  public notificationSubscription: Subscription

  private dragInitiated = false

  public icons = {
    eyeIcon: eyeIcon,
  }

  public isFieldLoading$ = this.fieldFacade.selectIsFieldLoading$

  public get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  public get isReviewPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isReviewPanelPopout')
  }

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  // private layoutReponseService: ReviewResponseProcessorUtilityService = inject(
  //   ReviewResponseProcessorUtilityService
  // )

  constructor(
    private utilityPanelFacade: UtilityPanelFacade,
    private startupFacade: StartupsFacade,
    private fieldFacade: FieldFacade,
    private changeDetectorRef: ChangeDetectorRef,
    private notificationService: NotificationService,
    private notificationTriggerService: NotificationTriggerService
  ) {
    this.listenToNotificationTriggerService()
  }

  public ngOnInit(): void {
    this.#selectViewWindowFullScreenStatus()
    this.#getActivePanel()
    this.#setUtitlityPanelUIState()
    this.#selectPanelSortOrder()
  }

  public onFilterChange(searchTerm: string): void {
    if (!searchTerm) {
      // If no search term, show all items
      this.filteredList.set(this.utilityPanelList())
      return
    }

    const searchLower = searchTerm.toLowerCase()
    const filteredItems = this.utilityPanelList().filter((item) =>
      item.toLowerCase().includes(searchLower)
    )

    this.filteredList.set(filteredItems)
  }

  #initializeLayoutPanels(): void {
    this.selectedUtilityPanels.set([])
    const selectedPanels = this.layoutState
      .userSelectedLayout()
      ?.layoutPanels.filter(
        (f) =>
          f.isSelected &&
          !ExcludedPanelsFromRight.includes(f.panelName) &&
          f.panelName !== 'Coding'
      )

    this.initialUtilityPanelItems =
      this.#mapLayoutPanelsToUtilityPanelItems(selectedPanels)
  }

  #mapLayoutPanelsToUtilityPanelItems(
    selectedLayoutPanels: LayoutPanel[]
  ): UtilityPanelItem[] {
    return selectedLayoutPanels?.map((panel, index) => ({
      title: titleMapping[panel.panelName],
      componentId: componentIdMapping[panel.panelName],
      expanded: true,
      isVisible: index === 0,
      hasPermission: true,
      order: index + 1,
      hasShowHideFields: panel.hasField,
    }))
  }

  public listenToNotificationTriggerService(): void {
    this.notificationSubscription =
      this.notificationTriggerService.notificationTrigger$
        .pipe(takeUntil(this.toDestroy$))
        .subscribe(({ content, type, hideAfter, width }) => {
          this.#showNotification(content, type, hideAfter, width)
        })
  }

  #sortPanels(): void {
    if (this.panelSortOrder && this.sortable && !this.isPanelSorted) {
      this.isPanelSorted = true
      this.sortable.data.sort((a, b) => {
        return (
          this.panelSortOrder?.indexOf(a.title) -
          this.panelSortOrder?.indexOf(b.title)
        )
      })
      this.sortable.data = [...this.sortable.data]
      this.changeDetectorRef.markForCheck()
    }
  }

  public onFullscreenClick(): void {
    this.#sendUtilityPanelFullscreenAction()
  }

  /** update the utility panel ui state
   * @returns {void} This method does not return anything.
   */
  #setUtitlityPanelUIState(): void {
    this.utilityPanelFacade.selectUtilityPanel$
      .pipe(
        filter((result) => Boolean(result)),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((utilityPanel: UtilityPanel) => {
        this.#updateUtilityPanelData(utilityPanel)
      })
  }

  /** sets the utility panel to fullscreen mode
   * @returns {void} This method does not return anything.
   */
  #sendUtilityPanelFullscreenAction(): void {
    // Popout window is not open then return
    if (this.isTagPanelPopout) return
    this.utilityPanelFacade.setIsUtilityPanelInFullScreen(true)
  }

  #setUtilityPanelData(): void {
    // bind the utility panel data
    const visiblePanels = this.initialUtilityPanelItems.filter(
      (x) => x.hasPermission
    )

    this.utilityPanelItems.set(visiblePanels)
    this.utilityPanelList.set(visiblePanels.map((x) => x.title))
    this.filteredList.set(this.utilityPanelList()) // Initialize filtered list with all items
    this.#setDefaultSelectedUtilityPanel()
  }

  #setDefaultSelectedUtilityPanel(): void {
    this.selectedUtilityPanels.set(
      this.initialUtilityPanelItems
        .filter((x) => x.isVisible)
        .map((x) => x.title)
    )
    this.utilityPanelItems().forEach((item) => {
      this.visibilityStatus[item.title] = item.isVisible
      this.expandedStatus[item.title] = item.expanded
    })
    this.changeDetectorRef.markForCheck()
  }

  #resetSelectedUtilityPanel(): void {
    this.selectedUtilityPanels.set(
      this.initialUtilityPanelItems
        .filter((x) => x.isVisible)
        .map((x) => x.title)
    )
    this.filterPanels()
    this.filteredList.set(this.utilityPanelList()) // Reset filtered list to all items
    this.changeDetectorRef.markForCheck()
  }

  #selectPanelSortOrder(): void {
    this.utilityPanelFacade.selectPanelSortOrder$
      .pipe(
        debounceTime(500),
        filter((panelSortOrder) => Boolean(panelSortOrder)),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((panelSortOrder) => {
        if (isEqual(this.panelSortOrder, panelSortOrder)) return
        this.panelSortOrder = panelSortOrder
        this.#sortPanels()
      })
  }

  #selectViewWindowFullScreenStatus(): void {
    this.utilityPanelFacade.selectViewerInFullScreen$
      .pipe(distinctUntilChanged(), takeUntil(this.toDestroy$))
      .subscribe((viewerInFullScreen) => {
        this.viewerInFullScreen = viewerInFullScreen
        this.changeDetectorRef.markForCheck()
      })
  }

  #updateUtilityPanelData(utilityPanel: UtilityPanel): void {
    this.visibilityStatus = cloneDeep(
      utilityPanel?.visibilityStatus || this.visibilityStatus
    )
    this.expandedStatus = utilityPanel?.expandedStatus || this.expandedStatus
    this.selectedUtilityPanels.set(
      this.initialUtilityPanelItems
        .filter((x) => this.visibilityStatus[x.title])
        .sort((a, b) => a.order - b.order)
        .map((x) => x.title)
    )
    this.tagCount = this.selectedUtilityPanels().length
    this.changeDetectorRef.markForCheck()
  }

  // This map associates componentId to dynamic components
  private componentsMap: { [key in UtilityPanelType]: Promise<Type<any>> } = {
    Metadata: this.metadataComponent,
    TagCoding: this.tagCodingContainerComponent,
    Notes: this.notesComponent,
    Family: this.parentChildComponent,
    Duplicate: this.duplicateComponent,
    DocumentHistory: this.documentHistoryComponent,
    SimilarDocuments: this.documentSimilarComponent,
    EmailThread: this.emailThreadComponent,
    NearDuplicate: this.nearDuplicateComponent,
    EdaiAiRelevance: this.edaiDocumentRelevancyComponent,
    EdaiAiPrivilege: this.edaiDocumentPrivilegeComponent,
    EdaiAiPIIDetect: this.edaiDocumentPIIComponent,
    EdaiAiPIIExtract: this.edaiDocumentPIIComponent,
    // Add other associations here
  }

  // Method to check if a component should be loaded
  public isComponentLoaded(item: any): boolean {
    return this.componentsMap[item.componentId] !== undefined
  }

  // Method to get the component to load
  public getComponent(item: any): Promise<Type<any>> {
    return this.componentsMap[item.componentId]
  }

  // Verify the UserRights.
  // Only authorized users can view the panels.
  #getActivePanel(): void {
    combineLatest([
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_REVIEW_METADATA_DOCPANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_REVIEW_DUPLICATES_DOCPANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_REVIEW_PARENTCHILD_DOCPANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_REVIEW_COMMENTS_DOCPANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_SIMILAR_DOCPANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_DOCUMENT_HISTORY_PANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_REVIEW_MESSAGETHREAD_DOCPANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_REVIEW_NEARDUPLICATES_DOCPANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_TAG_CODING_PANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_AI_RELEVANCE_PANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_AI_PRIVILEGED_PANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_AI_PII_DETECTION_PANEL
      ),
      this.startupFacade.hasGroupRight$(
        UserRights.ALLOW_TO_VIEW_AI_PII_EXTRACTION_PANEL
      ),
    ])
      .pipe(distinctUntilChanged(), takeUntil(this.toDestroy$))
      .subscribe(
        ([
          hasMetaDataRight,
          hasDuplicateRight,
          hasFamilyRight,
          hasNotesRight,
          hasSimilarDocumentsRight,
          hasDocumentHistoryRight,
          hasViewMessageThreadRight,
          hasNearDuplicateRight,
          hasViewTagCodingRight,
          hasViewAIRelevanceRight,
          hasViewAIPrivilegeRight,
          hasViewAIPIIDetectionRight,
          hasViewAIPIIExtractionRight,
        ]) => {
          const panelPermission: UtilityPanelPermission = {
            hasMetaDataRight,
            hasDuplicateRight,
            hasFamilyRight,
            hasNotesRight,
            hasSimilarDocumentsRight,
            hasDocumentHistoryRight,
            hasViewMessageThreadRight,
            hasNearDuplicateRight,
            hasViewTagCodingRight,
            hasViewAIRelevanceRight,
            hasViewAIPrivilegeRight,
            hasViewAIPIIDetectionRight,
            hasViewAIPIIExtractionRight,
          }
          this.#initializeLayoutPanels()
          this.#handlePanelPermission(panelPermission)
          const permittedPanels = this.initialUtilityPanelItems.filter(
            (item) => item.hasPermission
          )
          //initialize the utility panel items based on the user rights to make the first panel visible
          this.initialUtilityPanelItems = permittedPanels.map(
            (item, index) => ({
              ...item,
              isVisible: index === 0,
            })
          )
          this.#loadUtilityPanel()
        }
      )
  }

  #handlePanelPermission(panelPermission: UtilityPanelPermission): void {
    this.initialUtilityPanelItems.forEach((item) => {
      switch (item.title) {
        case UtilityPanelTitle.METADATA:
          item.hasPermission = panelPermission.hasMetaDataRight
          break
        case UtilityPanelTitle.DUPLICATE:
          item.hasPermission = panelPermission.hasDuplicateRight
          break
        case UtilityPanelTitle.FAMILY:
          item.hasPermission = panelPermission.hasFamilyRight
          break
        case UtilityPanelTitle.NOTES:
          item.hasPermission = panelPermission.hasNotesRight
          break
        case UtilityPanelTitle.SIMILAR_DOCUMENTS:
          item.hasPermission = panelPermission.hasSimilarDocumentsRight
          break
        case UtilityPanelTitle.DOCUMENT_HISTROY:
          item.hasPermission = panelPermission.hasDocumentHistoryRight
          break
        case UtilityPanelTitle.EMAIL_THREAD:
          item.hasPermission = panelPermission.hasViewMessageThreadRight
          break
        case UtilityPanelTitle.NEAR_DUPLICATE:
          item.hasPermission = panelPermission.hasNearDuplicateRight
          break
        case UtilityPanelTitle.TAG_CODING:
          item.hasPermission = panelPermission.hasViewTagCodingRight
          break
        case this.utilityPanelTitle.EDAI_AI_RELEVANCE:
          item.hasPermission = panelPermission.hasViewAIRelevanceRight
          break
        case this.utilityPanelTitle.EDAI_AI_Privilege:
          item.hasPermission = panelPermission.hasViewAIPrivilegeRight
          break
        case this.utilityPanelTitle.EDAI_AI_PII_DETECT:
          item.hasPermission = panelPermission.hasViewAIPIIDetectionRight
          break
        case this.utilityPanelTitle.EDAI_AI_PII_EXTRACT:
          item.hasPermission = panelPermission.hasViewAIPIIExtractionRight
          break
      }
    })
  }

  #loadUtilityPanel(): void {
    this.visibilityStatus = {}
    this.expandedStatus = {}
    this.#setUtilityPanelData()

    this.syncListItems()
  }

  // method to prevent closing the last panel
  public removeTag(event: RemoveTagEvent): void {
    if (this.selectedUtilityPanels().length === 0) {
      // Prevent removing the last item for tag mapper
      this.#setDefaultSelectedUtilityPanel() // Keep at least one
    } else if (this.tagCount > 2) {
      // if user wants to remove close all tags.
      event.preventDefault()
      this.#resetSelectedUtilityPanel()
    } else if (this.selectedUtilityPanels().length === 1) {
      // Prevent removing the last item
      event.preventDefault()
    }
  }

  // Method to update utilityPanelItems based on selection
  public onValueChange(event: string[]): void {
    this.tagCount = event.length
    if (event.length === 0) {
      this.#setDefaultSelectedUtilityPanel() // Keep at least one
      this.filterPanels()
    } else {
      this.selectedUtilityPanels.set(event)
      this.filterPanels()
    }
  }

  // Filter utilityPanelItems based on multiselect value
  private filterPanels(): void {
    this.utilityPanelItems().forEach((item) => {
      const updatedStatus = {
        ...this.visibilityStatus,
        [item.title]: this.selectedUtilityPanels().includes(item.title),
      }
      this.visibilityStatus = cloneDeep(updatedStatus)
    })

    this.utilityPanelFacade.setVisibilityItems(this.visibilityStatus)
    this.changeDetectorRef.markForCheck()
  }

  public removePanel(title: string, event: MouseEvent): void {
    event.stopPropagation() // Prevent event bubbling
    if (this.selectedUtilityPanels().length > 1) {
      this.selectedUtilityPanels.update((items) =>
        items.filter((item) => item !== title)
      )
      this.filterPanels()
    }
  }

  // Sync utiltyPanelList with utilityPanelItems
  private syncListItems(): void {
    this.utilityPanelList.set(
      this.utilityPanelItems().map((panel) => panel.title)
    )
  }

  // Handle the expand/collapse of panels
  public onAction(ev: ExpansionPanelActionEvent, title: string): void {
    //this.expandedStatus[title] = !this.expandedStatus[title]
    const updatedStatus = {
      ...this.expandedStatus,
      [title]: !this.expandedStatus[title],
    }
    this.expandedStatus = updatedStatus
    this.utilityPanelFacade.setExpandedItems(this.expandedStatus)
  }

  public onDragOver(event: DragOverEvent): void {
    event.preventDefault()
    this.sortable.moveItem(event.oldIndex, event.index)
    this.updateSortStateAfterReorder()
  }

  @DebounceTimer(500)
  private updateSortStateAfterReorder(): void {
    const panelSortOrder = this.sortable.data.map((s) => s.title)
    this.utilityPanelFacade.setPanelSortOrder(panelSortOrder)
  }

  public tagMapper(tags: any[]): any[] {
    return tags.length < 3 ? tags : [tags]
  }

  // On drag start event handler for button and prevent default action for kendo sortable
  public onDragStart(event: DragStartEvent): void {
    if (!this.dragInitiated) {
      event.preventDefault()
    }
    this.dragInitiated = false
  }

  public setDragInitiated(value: boolean): void {
    this.dragInitiated = value
  }

  #showNotification(
    content: string,
    type: NotificationType,
    delay: number,
    width: number
  ): void {
    this.notificationService.show({
      appendTo: this.appendTo,
      content: content,
      animation: { type: 'fade', duration: 300 },
      type: type, // Use Kendo Notification Type
      cssClass: 'v-custom-save-notification',
      hideAfter: delay,
      width: width,
    })
  }

  public panelFilterClick(
    panelType: UtilityPanelType,
    event: MouseEvent
  ): void {
    event.stopPropagation() // Prevent event bubbling
    this.utilityPanelFacade.triggerPanelFilterActionEvent(panelType)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.notificationSubscription.unsubscribe()
  }
}
