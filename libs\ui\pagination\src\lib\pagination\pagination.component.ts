import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  AfterViewInit,
  viewChild,
  signal,
  ViewEncapsulation,
} from '@angular/core'
import { Subject } from 'rxjs'
import { Page, PageArgs } from '../model/pagination.model'

@Component({
  selector: 'venio-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class PaginationComponent
  implements OnInit, OnChanges, OnDestroy, AfterViewInit
{
  @Output()
  public readonly pageChanged: EventEmitter<PageArgs> =
    new EventEmitter<PageArgs>()

  @Output()
  public readonly pageSizeChanged: EventEmitter<PageArgs> =
    new EventEmitter<PageArgs>()

  @Input() public pageSize: number

  @Input() public totalRecords: number

  @Input() public disabled = true

  @Input() public showPageJumper = true

  @Input() public showPageSize = true

  @Input() public showRowNumberInputBox = true // if true, then show text box to input value otherwise hide textbox and show first row number only

  @Input() public currentPage: number

  public totalPages: number

  public Page = Page

  public sizes = [10, 15, 25, 50, 100]

  public firstDocumentOfCurrentPage: number

  private unsubscribe: Subject<void> = new Subject<void>()

  public mirror = viewChild<ElementRef>('mirror')

  public readonly inputWidth = signal(40)

  public ngAfterViewInit(): void {
    this.#updateInputWidth()
  }

  #updateInputWidth(): void {
    // Allow the view to render first before measuring the width
    // This is necessary to ensure that the mirror element has been rendered and the width can be accurately measured
    setTimeout(() => {
      if (this.mirror()?.nativeElement) {
        const mirrorWidth = this.mirror().nativeElement.offsetWidth
        this.inputWidth.set(Math.max(40, mirrorWidth + 20))
      }
    })
  }

  public ngOnChanges(changes: SimpleChanges): void {
    this.totalPages = Math.ceil(this.totalRecords / this.pageSize) || 1
    if (this.totalRecords <= 0) {
      this.firstDocumentOfCurrentPage = 0
    } else if (
      this.firstDocumentOfCurrentPage === 0 ||
      this.firstDocumentOfCurrentPage === 1 ||
      this.currentPage === 1
    )
      this.firstDocumentOfCurrentPage = 1

    if (this.firstDocumentOfCurrentPage > this.totalRecords) {
      this.currentPage = this.currentPage - 1
      this.setFirstDocumentNumber()
    }

    this.#updateInputWidth()
  }

  public ngOnInit(): void {
    this.currentPage = this.currentPage || 1

    if (this.totalRecords <= 0) this.firstDocumentOfCurrentPage = 0
    else this.setFirstDocumentNumber()
  }

  public ngOnDestroy(): void {
    this.unsubscribe.next()
    this.unsubscribe.complete()
  }

  public goTo(page: Page): void {
    switch (page) {
      case Page.First:
        this.currentPage = 1
        this.pageChanged.next({
          pageNumber: 1,
          pageSize: this.pageSize,
        })
        this.setFirstDocumentNumber()
        break
      case Page.Previous:
        this.currentPage = this.currentPage > 1 ? --this.currentPage : 1
        this.pageChanged.next({
          pageNumber: this.currentPage,
          pageSize: this.pageSize,
        })
        this.setFirstDocumentNumber()
        break
      case Page.Next:
        this.currentPage =
          this.currentPage < this.totalPages
            ? ++this.currentPage
            : this.totalPages
        this.pageChanged.next({
          pageNumber: this.currentPage,
          pageSize: this.pageSize,
        })
        this.setFirstDocumentNumber()
        break
      case Page.Last:
        this.currentPage = this.totalPages
        this.pageChanged.next({
          pageNumber: this.currentPage,
          pageSize: this.pageSize,
        })
        this.setFirstDocumentNumber()
        break
      default:
        break
    }
  }

  public onChange(page: number): void {
    if (page > 0 && page <= this.totalPages) {
      this.currentPage = page
    } else this.currentPage = this.totalPages
    this.pageChanged.next({
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
    })
  }

  public onPageSizeChange(selPageSize: number): void {
    this.currentPage = Math.ceil(
      (this.pageSize * (this.currentPage - 1) + 1) / selPageSize
    )
    this.pageSize = selPageSize
    this.pageSizeChanged.next({
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
    })

    this.setFirstDocumentNumber()
  }

  private setFirstDocumentNumber(): void {
    this.firstDocumentOfCurrentPage = this.pageSize * (this.currentPage - 1) + 1
  }

  public onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      if (+this.firstDocumentOfCurrentPage <= 0)
        this.firstDocumentOfCurrentPage = 1
      else if (+this.firstDocumentOfCurrentPage > this.totalRecords)
        this.firstDocumentOfCurrentPage = this.totalRecords

      this.currentPage = Math.ceil(
        +this.firstDocumentOfCurrentPage / this.pageSize
      )

      this.pageSizeChanged.next({
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
      })

      this.setFirstDocumentNumber()
    }

    this.#updateInputWidth()
  }
}
