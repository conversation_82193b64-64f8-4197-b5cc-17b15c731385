import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  effect,
  inject,
  Injector,
  HostListener,
  OnDestroy,
  OnInit,
  signal,
  viewChild,
} from '@angular/core'
import {
  ColumnBase,
  SelectableSettings,
  SelectionChangeItem,
  TreeListComponent,
  TreeListItem,
} from '@progress/kendo-angular-treelist'
import {
  DocumentsFacade,
  DocumentsService,
  EmailThreadVisibleType,
  FieldFacade,
  ReviewFacade,
  ReviewParamService,
  ReviewViewType,
  SearchFacade,
  SearchResultFacade,
  UIService,
  StartupsFacade,
  DocumentSearchScopeModel,
  SearchResponseModel,
  SearchResponseData,
  CompositeLayoutState,
  ReviewSetStateService,
} from '@venio/data-access/review'

import {
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  firstValueFrom,
  from,
  map,
  Subject,
  take,
  takeUntil,
} from 'rxjs'
import { difference, intersection, isEqual } from 'lodash'
import { PageArgs } from '@venio/ui/pagination'
import { ActivatedRoute, Router } from '@angular/router'
import { Location } from '@angular/common'
import { DialogService } from '@progress/kendo-angular-dialog'
import {
  CommandEventTypes,
  PageControlActionType,
  TagSummaryActionType,
} from '@venio/shared/models/constants'
import { pairwise, switchMap, tap, withLatestFrom } from 'rxjs/operators'
import { FieldDateConversionService } from '@venio/util/utilities'
import * as emailThreadColorConstant from '@venio/data-access/document-utility'
import {
  DocumentTagFacade,
  DocumentTagService,
  ReviewPanelViewState,
} from '@venio/data-access/document-utility'
import { NotificationDialogComponent } from '@venio/feature/notification'
import { GroupStackType, ResponseModel } from '@venio/shared/models/interfaces'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { toSignal } from '@angular/core/rxjs-interop'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { CommandsFacade } from '@venio/data-access/common'
import {
  WindowMessage,
  WindowMessageContent,
  WindowMessageType,
  WindowMessengerService,
} from '../../../services/window.messenger.service'
import { LocalStorage } from '@venio/shared/storage'
import { UtilityPanelStoreUpdateService } from '../../document-utility/utility-services/utility-panel-store-update'
import { WindowManagerService } from '../../../services/window.manager.service'

@Component({
  selector: 'venio-document-table',
  templateUrl: './document-table.component.html',
  styleUrls: ['./document-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentTableComponent implements OnInit, OnDestroy {
  private treeList = viewChild(TreeListComponent)

  public documentMenuLazy = import(
    '../document-menu/document-menu.component'
  ).then((m) => m.DocumentMenuComponent)

  public tagSummaryComponent = import(
    '../../document-utility/tag-summary/tag-summary-container/tag-summary-container.component'
  ).then((m) => m.TagSummaryContainerComponent)

  public tableData = signal([])

  public columns = signal<string[]>([])

  public selectableSettings: SelectableSettings = {
    mode: 'row',
    multiple: true,
    drag: false,
  }

  public selectedDocumentPageMap = new Map<number, number>()

  public currentPageSelectedDocuments = []

  public totalRecords = 0

  public pageSize: number

  public currentPage: number

  public documentTableWidth: number

  private toDestroy$: Subject<void> = new Subject<void>()

  private readonly isSearchLoading = toSignal(
    this.searchFacade.getIsSearchLoading$.pipe(
      filter((loading) => typeof loading === 'boolean')
    ),
    {
      initialValue: true,
    }
  )

  private readonly areDocumentsLoading = toSignal(
    this.searchResultFacade.selectAreDocumentsLoading$.pipe(
      filter((loading) => typeof loading === 'boolean')
    ),
    {
      initialValue: true,
    }
  )

  public readonly loadingDocuments = computed(() => {
    return this.isSearchLoading() || this.areDocumentsLoading()
  })

  public readonly noRecordsAvailable = computed(() => {
    const isSearchLoading = this.isSearchLoading()
    const areDocumentsLoading = this.areDocumentsLoading()
    const tableData = this.tableData()

    return !isSearchLoading && !areDocumentsLoading && !tableData?.[0]
  })

  public projectId: number

  private previousProjectId: number

  private currentFileId: number

  private isBatchSelected = false

  private userPageControlAction: PageControlActionType

  private isNavigatedFromViewer = true

  private isPopoutWindowNavigation = false

  private isDocumentNavigationNeeded = true

  private currentDocumentSearchScope: DocumentSearchScopeModel

  private searchResponse: SearchResponseModel

  private searchResults: SearchResponseData[]

  public svgIconForTagSummary = signal([
    {
      actionType: TagSummaryActionType.TAG,
      iconPath: 'assets/svg/icon-tag.svg',
    },
    {
      actionType: TagSummaryActionType.FOLDER,
      iconPath: 'assets/svg/icon-document-folder.svg',
    },
    {
      actionType: TagSummaryActionType.TAGFOLDER,
      iconPath: 'assets/svg/icon-tag-folder.svg',
    },
  ])

  // Declaration of a public variable to store the count of selected documents
  public selectedDocumentCount = signal(0)

  public idField = signal('__FileID') // field to be used as id for the treelist

  public parentIdField = '__treelistParentId' // field to be used as parent id for the treelist

  public isEmailThreadView = signal(false) // flag to indicate if the view is email thread view or normal view

  public isInclusiveEmailOnlyView = signal(false) // flag to indicate if the view is inclusive email only view

  public numberOfEmailInCurrentPage = 50 // number of email threads to show. This will be used in email thread view as page size as opposed to the total number of documents.

  public expandedKeys = [] //used to bind and clear the expanded rows (needs to be collapsed on initial load instead of restoring the previous state)

  private get docShareToken(): number {
    return this.activatedRoute.snapshot.queryParams['docShareToken']
  }

  public emailThreadColorConstant = emailThreadColorConstant // constant for email thread view colors

  public missingEmailCount: number | null = null // count of missing emails in the email thread to be shown in the popover

  public inclusiveEmailCount: number | null = null // count of inclusive emails in the email thread to be shown in the popover

  public childEmailCount: number | null = null // count of total emails in the email thread to be shown in the popover

  public generatedEmailCount: number | null = null // count of generated emails in the email thread to be shown in the popover

  private get moduleData(): number {
    return this.activatedRoute.snapshot.queryParams['moduleData']
  }

  public isReviewForTagRuleConflicted = signal(false)

  private isTagRuleIdFoundInSyntax = signal(false)

  public get isReviewPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isReviewPanelPopout')
  }

  public reviewSetState = inject(ReviewSetStateService)

  private readonly commandFacade = inject(CommandsFacade)

  /**
   * A signal that determines if the current search was triggered from eDAI status > privilege > job name click.
   * This signal processes the breadcrumb stack through a series of reactive operators.
   *
   *
   * @description
   * Pipeline steps:
   * 1. Observes changes in the breadcrumb stack via {@link BreadcrumbFacade.selectBreadcrumbStack$}
   * 2. Filters for stacks containing AI privileged search entries
   * 3. Extracts the specific AI privileged search entry from the stack
   * 4. Prevents duplicate emissions when the groupStackType remains unchanged
   * 5. Switches to viewer panel notifications from CommandFacade
   * 6. Final validation to ensure:
   *    - The search originated from eDAI privilege job name click
   *    - Table data exists and is not empty
   *
   *
   * @see {@link BreadcrumbFacade.selectBreadcrumbStack$} - Source observable for breadcrumb stack
   * @see {@link CommandsFacade.selectNotifyEnterViewerPanel$} - Viewer panel notifications
   * @see {@link GroupStackType.AI_PRIVILEGE_SEARCH} - AI privilege search type constant
   */
  private readonly isCurrentSearchAiPrivileged = toSignal(
    this.breadcrumbFacade.selectBreadcrumbStack$.pipe(
      filter((stack) =>
        stack?.some(
          (s) => s.groupStackType === GroupStackType.AI_PRIVILEGE_SEARCH
        )
      ),
      map((stack) =>
        stack.find(
          (s) => s.groupStackType === GroupStackType.AI_PRIVILEGE_SEARCH
        )
      ),
      switchMap(() => this.commandFacade.selectNotifyEnterViewerPanel$),
      filter(
        (ce) =>
          ce?.['hasAiPrivilegeJobNameClicked'] && this.tableData()?.length > 0
      )
    )
  )

  constructor(
    private dialogService: DialogService,
    private searchFacade: SearchFacade,
    private searchResultFacade: SearchResultFacade,
    private documentsFacade: DocumentsFacade,
    private docuemtTagFacade: DocumentTagFacade,
    private fieldFacade: FieldFacade,
    private startupsFacade: StartupsFacade,
    private layoutState: CompositeLayoutState,
    private reviewPanelViewState: ReviewPanelViewState,
    private documentsService: DocumentsService,
    private reviewParamService: ReviewParamService,
    private messengerService: WindowMessengerService,
    private windowManager: WindowManagerService,
    private utilityPanelStoreUpdateService: UtilityPanelStoreUpdateService,
    private uis: UIService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private location: Location,
    private activatedRoute: ActivatedRoute,
    private fieldDateConversionService: FieldDateConversionService,
    private reviewFacade: ReviewFacade,
    private documentTagService: DocumentTagService,
    private iframeMessengerService: IframeMessengerService,
    private breadcrumbFacade: BreadcrumbFacade,
    private injector: Injector
  ) {}

  @HostListener('window:beforeunload')
  public readonly beforeUnload = (): void => {
    // Close the popout window.
    // There is no need to keep the popout window open when the user refreshes the main window.
    this.#reviewPanelWindowUnloadHandler()
  }

  public ngOnInit(): void {
    this.init()
    this.#selectTagRuleIdPatternList()
    this.#enterViewerIfAiPrivSearch()
  }

  /**
   * Sets up an effect to monitor AI privileged searches.
   * If an AI privileged search is detected in the breadcrumb stack,
   * navigates to the document viewer with the relevant documents.
   * @see {@link isCurrentSearchAiPrivileged}
   * @returns {void}
   */
  #enterViewerIfAiPrivSearch(): void {
    effect(
      () => {
        // We need to reset this as soon as it has been hit
        this.commandFacade.resetCommandState(
          CommandEventTypes.NotifyEnterViewerPanel
        )

        const aiPrivilegedSearch = this.isCurrentSearchAiPrivileged()
        const firstRow = this.tableData()?.[0]

        if (!aiPrivilegedSearch || !firstRow || this.areDocumentsLoading())
          return

        this.showDocumentViewer(firstRow)
      },
      { injector: this.injector, allowSignalWrites: true }
    )
  }

  #handleProjectIdQueryParamChange(): void {
    this.activatedRoute.queryParams
      .pipe(
        filter(
          (q) => Boolean(q['projectId']) && this.projectId !== +q['projectId']
        ),
        distinctUntilChanged((a, b) => +a['projectId'] === +b['projectId']),
        takeUntil(this.toDestroy$)
      )
      .subscribe((params) => {
        this.reviewFacade.setReviewViewType(ReviewViewType.Search)
        // reseting document table state like selected documents, flags and so on
        this.#resetDocumentStates()
        // reseting search control form values
        this.#resetSearchControlFormValues()
        this.projectId = +params['projectId']
        this.#resetDocumentPageSelection()
        this.isDocumentNavigationNeeded = true
        this.isNavigatedFromViewer = false
        this.#updateTreeViewBindings(ReviewViewType.Search, true)
      })
    this.previousProjectId =
      +this.activatedRoute.snapshot.queryParams['projectId']
  }

  #resetDocumentStates(): void {
    this.documentsFacade.resetDocumentState([
      'selectedDocuments',
      'isBatchSelected',
      'currentDocument',
      'currentDocumentName',
      'currentFileName',
      'unselectedDocuments',
      'isBulkDocument',
      'menuEventPayload',
      'tagSummary',
      'tagSummaryErrorResponse',
      'tagHistory',
      'tagHistoryErrorResponse',
      'folderSummaryErrorResponse',
      'currentDocumentTablePage',
    ])
    this.docuemtTagFacade.resetDocumentTagState(['projectTags'])
  }

  #resetSearchControlFormValues(): void {
    this.searchFacade.resetSearchState('searchFormValues')
  }

  /**
   * Notifies the parent window that the 'moduleData' query parameter has been removed from the URL.
   *
   * @param {number} projectId - The identifier of the current project.
   * @returns {void}
   */
  #notifyParentWindow(projectId: number): void {
    this.iframeMessengerService.sendMessage({
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content: {
          isUrlParameterChange: true,
          // URL parameters such as query param or route path to be sent to the parent window
          // which is removed from the current URL so the parent window can update the URL accordingly
          removedParameters: ['moduleData'],
        },
      },
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
  }

  /**
   * Removes the 'moduleData' query parameter from the browser URL without triggering a router event,
   * and notifies the parent window of this change.
   *
   * Once the data is used and stored in state, we no longer need the 'moduleData' query parameter in the URL.
   *
   * @returns {void}
   */
  #removeModuleDataQueryParam(): void {
    if (this.isReviewForTagRuleConflicted()) {
      // Remove the 'moduleData' query parameter from the URL
      const urlTree = this.router.createUrlTree([], {
        relativeTo: this.activatedRoute,
        queryParams: { moduleData: null },
      })

      // Update the browser URL without navigating
      this.location.replaceState(urlTree.toString())
      this.#notifyParentWindow(this.projectId)
    }
  }

  #setTagRuleConflictFlag(): void {
    this.isReviewForTagRuleConflicted.set(
      this.isTagRuleIdFoundInSyntax() ||
        this.documentTagService.isReviewForTagRuleConflicted()
    )
  }

  private init(): void {
    this.#setTagRuleConflictFlag()
    this.#selectResetDocumentSelection()
    this.#getUserRights()
    this.#getSearchResponseData()
    this.#getSelectedDocuments()
    this.#getPermittedFields()
    this.#notifyDocumentChangeToReviewPopoutPanel()
    this.#handleReviewPopoutPanelData()
    this.#removeModuleDataQueryParam()

    this.searchFacade.clearSearchResult$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.documentsFacade.setCurrentDocument(-1)
        this.tableData.set([])
        this.totalRecords = 0
        this.pageSize = null
      })

    this.reviewFacade.getReviewViewType$
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((viewType) => {
        this.isEmailThreadView.set(viewType === ReviewViewType.EmailThread)
      })
    this.reviewParamService.projectId
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((projectId) => {
        this.projectId = projectId
      })
    this.loadDataSource()
    this.initSlices()

    // Combine observables from different facades
    combineLatest([
      // Observable for batch selection status
      this.documentsFacade.getIsBatchSelected$,
      // Observable for selected documents
      this.documentsFacade.getSelectedDocuments$,
      // Observable for unselected documents
      this.documentsFacade.getUnselectedDocuments$,
      // Observable for total search count
      this.searchFacade.getTotalHitCount$,
    ])
      .pipe(
        debounceTime(50),
        // Complete the subscription when component is destroyed
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([
          // Boolean indicating batch selection status
          isBatchSelected,
          // Array of selected documents
          selectedDocuments,
          // Array of unselected documents
          unselectedDocuments,
          // Total number of search hits
          totalSearchCount,
        ]) => {
          this.isBatchSelected = isBatchSelected

          // Calculate selected document count based on batch selection status
          if (isBatchSelected) {
            // If batch is selected, calculate selected documents as total count minus unselected documents
            this.selectedDocumentCount.set(
              totalSearchCount - unselectedDocuments.length
            )
          } else {
            // If batch is not selected, count selected documents
            this.selectedDocumentCount.set(selectedDocuments.length)
          }

          //While switching cases from drop down, currentDocument = null(default value) causing transcript not loading hence set currentDocument = -1
          if (!this.selectedDocumentCount()) {
            this.documentsFacade.setCurrentDocument(-1)
          }
          this.cdr.markForCheck()
        }
      )
  }

  private initSlices(): void {
    this.uis.splitterPaneSubject
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((width) => {
        this.cdr.markForCheck()
        this.documentTableWidth = width
      })
    combineLatest([
      this.searchFacade.getSearchInitialParameters$,
      this.searchResultFacade.getCurrentDocumentTablePaging$,
    ])
      .pipe(
        filter(([result, paging]) => !!result),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([result, paging]) => {
        this.cdr.markForCheck()
        // in the case of email thread view, the total records is the number of email threads instead of documents
        this.totalRecords = this.isEmailThreadView()
          ? result.totalThreadCount
          : result.totalHitCount
        this.pageSize = paging?.pageSize || result.pageSize
        this.currentPage = paging?.pageNumber || 1

        // if the pageSize is not set in the paging, then set the pageSize from the search result
        // and should only be set once
        if (result.pageSize && !paging?.pageSize) {
          this.searchResultFacade.storeDocumentTablePaging(
            this.currentPage,
            result.pageSize
          )
        }

        // we need to also check whether to show tag conflicts link column
        // after search is performed because it might be reset action
        this.#setTagRuleConflictFlag()
      })
    this.documentsService.updateDocumentSelection$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        if (this.isEmailThreadView()) {
          this.updateSelectionForEmailThreadView()
        } else {
          this.updateDocumentTableSelection()
        }
      })

    this.#updateDocumentTableUI()
    this.#handleReviewViewTypeChange()
    this.#handleProjectIdQueryParamChange()
    this.#setInitialView()
    this.#handleVisibleEmailTypeChange()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public documentTrackByFn = (index: number, item: TreeListItem): string => {
    const idField = this.idField()
    return `${item.data[idField]}_${index}`
  }

  private loadDataSource(): void {
    combineLatest([
      this.searchResultFacade.getSearchResultFieldValues,
      this.fieldFacade.getAllFields$,
    ])
      .pipe(
        filter(
          ([fieldValues, allFields]) =>
            !!fieldValues && !!allFields && !this.areDocumentsLoading()
        ),
        switchMap(([fieldValues, allFields]) => {
          return from(
            this.fieldDateConversionService.convertFieldDates(
              allFields,
              fieldValues
            )
          )
        }),
        // Small window to avoid extensive processing by multiple events being triggered
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe((convertedFieldValues) => {
        this.numberOfEmailInCurrentPage = convertedFieldValues.length
        if (!convertedFieldValues?.length) {
          //When no documents are selected, set the document as -1 and it will clear all the review panels.
          this.documentsFacade.setCurrentDocument(-1)
          this.tableData.set([])
          this.searchFacade.IsSearchLoading(false)
          return
        }

        // if view is email thread view then flatten the data to display hierarchy in single level (all emails under single parent)
        if (this.isEmailThreadView()) {
          this.tableData.set(
            this.flattenDataForEmailThread(convertedFieldValues)
          )
        } else {
          // if view is not email thread view then set the __treelistParentId value to null which is used in treelist to bind parent
          this.tableData.set(
            convertedFieldValues.map((item) => ({
              ...item,
              __treelistParentId: null,
            }))
          )
        }

        if (this.isEmailThreadView()) {
          this.updateSelectionForEmailThreadView()
        } else {
          this.updateDocumentTableSelection()
        }

        this.#synchronizeDocumentSelection()

        // If there is data, allow it to fill and turn off the loading spinner
        this.searchFacade.IsSearchLoading(false)

        this.#populateColumns(convertedFieldValues)
      })
  }

  /**
   * Populates the columns for the document table based on the converted field values.
   *
   * @remarks
   *  Allow HTML view to take some time for recalculating the columns previously bound
   *  Once new dataset is loaded, we need to update the columns with new data,
   *  so we do it as a background task to avoid treelist not being able to recalculate the columns rendering
   *  which results in column inconsistency.
   * @param {unknown[]} convertedFieldValues - Array of converted field values from the search result.
   * @returns {void}
   */
  #populateColumns(convertedFieldValues: unknown[]): void {
    const columnMaps =
      Object?.keys(convertedFieldValues[0]).filter(
        (field) =>
          !field.startsWith('__') && field !== 'FileID' && field !== 'seq_no'
      ) || []

    // we need to clear the columns trigger reactive changes on signal.
    this.columns.set([])

    setTimeout(() => {
      this.columns.set(columnMaps)

      // once the columns are set, we need to scroll to the first column
      this.treeList()?.scrollTo({ column: 0 })
    })
  }

  /**
   * Triggers search when the email thread view mode is changed from 'inclusive email only' to 'all emails' and vice versa
   * @returns {void}
   */
  #handleVisibleEmailTypeChange(): void {
    this.reviewFacade.getVisibleEmailType$
      .pipe(
        withLatestFrom(this.reviewFacade.getReviewViewType$),
        pairwise(),
        filter(
          ([[prevEmailType, prevViewType], [currEmailType, currViewType]]) =>
            currViewType === ReviewViewType.EmailThread &&
            (prevEmailType !== currEmailType || prevViewType !== currViewType)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([[prevEmailType, prevViewType], [currEmailType, currViewType]]) => {
          this.isInclusiveEmailOnlyView.set(
            currEmailType === EmailThreadVisibleType.InclusiveEmailOnly
          )
          this.searchFacade.IsSearchLoading(true) // show loading spinner
          this.searchFacade.doSearch() // trigger search
        }
      )
  }

  #selectTagRuleIdPatternList(): void {
    this.searchFacade.selectTagRuleIdPatternList$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((tagRuleIdPatternList) => {
        this.isTagRuleIdFoundInSyntax.set(
          tagRuleIdPatternList.ruleIds.length > 0
        )

        this.#setTagRuleConflictFlag()
      })
  }

  /**
   * Will update parent id of the data such that all emails under root email will be child of that email regardless of whether the email is child or grandchild
   * @param {any[]} data - The data to be binded to the treelist
   * @returns {any[]} - The flattened data
   */
  private flattenDataForEmailThread(data: any[]): any[] {
    const tableData = data.map((item) => {
      return {
        ...item,
        FileID: +item['FileID'], // converting FileID to number which for some reason is string until this point
        __FileID: +item['FileID'],
        __treelistParentId: item.__ParentId === -1 ? null : item.__ParentId, // adding _treelistParentId field to bind to treelist as it requires parent to be null instead of -1
        __missingEmailCount: 0, // count of missing emails in the email thread
        __inclusiveEmailCount: 0, // count of inclusive emails in the email thread
        __totalEmailCount: 0, // total number of emails in the email thread
        __totalGeneratedEmailCount: 0, // count of generated emails in the email thread
        __emailCategory: (item['__emailCategory'] || '').toUpperCase(),
        __doctype: (item['__doctype'] || '').toUpperCase(),
        __childCount: +item['__childCount'] || 0, // number of children for the email
        __subjectTextColor: this.#getTextColor(item), // set the color of the email subject based on the email type
      }
    })
    // get all root email
    const rootNodes: any[] = tableData.filter(
      (node) => node.__treelistParentId === null
    )

    // loop through all root email and update the parent id of all children and grandchildren to the root email, and set the missing and inclusive email count
    rootNodes.forEach((rootNode) => {
      this.updateEmailThreadParentId(rootNode, tableData, rootNode.__Id)

      this.#updateSummaryForRootEmail(rootNode, tableData)
    })

    return tableData
  }

  /**
   * Returns the color code for the email subject text based on whether it is a reply, forward or missing email (dummy email)
   * @param {any} dataItem data item
   * @returns {string} color code for the email subject text
   */
  #getTextColor(dataItem: any): string {
    const emailSubject = (dataItem['__emailSubject'] || '').toLowerCase()
    const fileId = +dataItem['__FileID']

    if (fileId > 0 && emailSubject.startsWith('re:'))
      return this.emailThreadColorConstant.replyEmailTextColor
    else if (fileId > 0 && emailSubject.startsWith('fw:'))
      return this.emailThreadColorConstant.forwardEmailTextColor
    else if (fileId < 0)
      return this.emailThreadColorConstant.missingEmailTextColor

    return ''
  }

  /**
   * Calculates the summary for the root email based on the children emails
   * @param {any} rootNode root email node
   * @param {any[]} tableData data that is binded to the treelist
   * @returns {void}
   */
  #updateSummaryForRootEmail(rootNode: any, tableData: any[]): void {
    // Initialize counts
    let missingEmailCount = 0
    let inclusiveEmailCount = 0
    let totalGeneratedEmailCount = 0
    let totalEmailCount = 0

    // Iterate through tableData to calculate counts
    tableData.forEach((node) => {
      // check if the node is a child of the root email using '__treelistParentId' or the node is the root email itself using '__Id'
      if (
        node.__treelistParentId === rootNode.__Id ||
        node.__Id === rootNode.__Id
      ) {
        switch (node.__doctype) {
          case 'DUMMY_EMAIL':
            missingEmailCount++
            break
          case 'INCLUSIVE_EMAIL':
            inclusiveEmailCount++
            break
          case 'GENERATED_EMAIL':
            totalGeneratedEmailCount++
            break
        }
        totalEmailCount++
      }
    })

    // Assign counts to rootNode properties
    rootNode.__missingEmailCount = missingEmailCount
    rootNode.__inclusiveEmailCount = inclusiveEmailCount
    rootNode.__totalGeneratedEmailCount = totalGeneratedEmailCount
    rootNode.__totalEmailCount = totalEmailCount
  }

  /**
   * Sets count of missing, inclusive, child and generated emails to be shown in the popover
   * @param {any} dataItem data item that is being hovered
   * @returns {void}
   */
  public showPopover(dataItem: any): void {
    this.cdr.markForCheck()
    this.missingEmailCount = dataItem.__missingEmailCount
    this.inclusiveEmailCount = dataItem.__inclusiveEmailCount
    this.childEmailCount = dataItem.__totalEmailCount
    this.generatedEmailCount = dataItem.__totalGeneratedEmailCount
  }

  /**
   * Recursive function to update the parent id of all children and grandchildren to set to root email (rootParentId)
   * @param {any} node - The root email under which all emails will be binded
   * @param {any[]} data - The email thread data that needs to be flattened
   * @param {number} rootParentId - Id of the root email
   * @returns {void}
   */
  private updateEmailThreadParentId(
    node: any,
    data: any[],
    rootParentId: number
  ): void {
    const children: any[] = data.filter(
      (child) => child.__treelistParentId === node.__Id
    )
    children.forEach((child) => {
      const descendants: any[] = data.filter(
        (descendant) => descendant.__treelistParentId === child.__Id
      )
      descendants.forEach((descendant) => {
        descendant.__treelistParentId = rootParentId
        this.updateEmailThreadParentId(descendant, data, rootParentId) // Recursively adjust descendants
      })
      child.__treelistParentId = rootParentId
      this.updateEmailThreadParentId(child, data, rootParentId) // Recursively adjust children's descendants
    })
  }

  /**
   * Returns the svg path for the email based on the category and child count
   * @param {any} dataItem - The data item
   * @returns {string} - The svg path
   */
  public getEmailIcon(dataItem: any): string {
    const category = dataItem['__emailCategory']
    const childCount = dataItem['__childCount']

    let iconPath = ''

    switch (category) {
      case 'INTERNAL':
      case 'EXTERNAL':
        iconPath =
          childCount === 0
            ? 'assets/svg/email.svg'
            : 'assets/svg/email-attachment.svg'
        break
      case 'INBOUND':
        iconPath =
          childCount === 0
            ? 'assets/svg/email-inbound.svg'
            : 'assets/svg/email-inbound-with-attachment.svg'
        break
      case 'OUTBOUND':
        iconPath =
          childCount === 0
            ? 'assets/svg/email-outbound.svg'
            : 'assets/svg/email-outbound-with-attachment.svg'
        break
    }

    return iconPath
  }

  /**
   * Returns the color code for the email subject text based on whether it is a reply, forward or missing email (dummy email)
   * Additionally, if the email thread is being filtered for viewing only inclusive email, the non-inclusive parent email will be shown in grey color
   * @param {any} dataItem current data item
   * @returns {string} color code for the email subject text
   */
  public getSubjectTextColor(dataItem: any): string {
    // in case of inclusive email only view, the non-inclusive parent email will be shown in grey color
    if (
      this.isInclusiveEmailOnlyView() &&
      dataItem.__doctype !== 'INCLUSIVE_EMAIL'
    ) {
      return emailThreadColorConstant.nonInclusiveEmailTextColor
    }

    // else return the color based on the email type that is set earlier while binding the data
    return dataItem.__subjectTextColor as string
  }

  /**
   * Returns the icon color based on the email type
   * @param {any} dataItem - The data item
   * @returns {string} The color of email icon
   */
  public getIconColor(dataItem: any): string {
    const docType = (dataItem['__doctype'] || '').toUpperCase()

    switch (docType) {
      case 'INCLUSIVE_EMAIL':
        return this.emailThreadColorConstant.inclusiveEmailIconColor
      case 'GENERATED_EMAIL':
        return this.emailThreadColorConstant.generatedEmailIconColor
      case 'DUMMY_EMAIL':
        return this.emailThreadColorConstant.missingEmailIconColor
      default:
        return this.emailThreadColorConstant.defaultEmailIconColor
    }
  }

  /**
   *  Returns the title for the email icon
   * @param {any} dataItem - The data item
   * @returns {string} - The title of the icon
   */
  public getIconTitle(dataItem: any): string {
    const docType = dataItem['__doctype']

    switch (docType) {
      case 'INCLUSIVE_EMAIL':
        return 'Inclusive Email'

      case 'GENERATED_EMAIL':
        return 'Generated Email'

      case 'DUMMY_EMAIL':
        return 'Missing Email'

      default:
        return this.getDocTypeTooltip(
          dataItem['__emailCategory'],
          dataItem['__childCount']
        )
    }
  }

  /**
   * Returns the tooltip for the email icon based on the category and child count
   * @param {string} category category of email
   * @param {number} childCount number of children for the email
   * @returns {string} tooltip for the email icon
   */
  private getDocTypeTooltip(category: string, childCount: number): string {
    let toolTip = ''

    switch (category) {
      case 'INTERNAL':
      case 'EXTERNAL':
        toolTip = childCount === 0 ? 'Email' : 'Email With Attachment'
        break
      case 'INBOUND':
        toolTip =
          childCount === 0 ? 'Inbound Email' : 'Inbound Email With Attachment'
        break
      case 'OUTBOUND':
        toolTip =
          childCount === 0 ? 'Outbound Email' : 'Outbound Email With Attachment'
        break
    }

    return toolTip
  }

  /**
   * Handles the change in review view type. The change is done from the switch view component.
   * Will switch the view type to email thread view or search view based on the selected view type.
   * @returns {void}
   */
  #handleReviewViewTypeChange(): void {
    this.reviewFacade.getReviewViewType$
      .pipe(
        withLatestFrom(this.reviewFacade.getIsSwitchingView$),
        tap(([view, switching]) => {
          this.isEmailThreadView.set(view === ReviewViewType.EmailThread)
        }),
        filter(
          ([view, switching]) =>
            switching &&
            (view === ReviewViewType.EmailThread ||
              view === ReviewViewType.Search)
        ),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([nextViewType, _]) => {
        this.searchFacade.IsSearchLoading(true) // show loading spinner
        this.#switchView(nextViewType) // switch view type
      })
  }

  /**
   * Handles close email thread view button.
   * @returns {void}
   */
  public closeEmailThreadView(): void {
    this.reviewFacade.isSwitchingView(true)
    this.searchFacade.IsSearchLoading(true) // show loading spinner
    this.reviewFacade.setReviewViewType(ReviewViewType.Search) // update state of reviewviewtype
    this.#switchView(ReviewViewType.Search)
  }

  /**
   * Updates tree list bindings based on the view type and triggers search
   * @param {ReviewViewType} viewType - The view type
   * @returns {void}
   */
  #switchView(viewType: ReviewViewType): void {
    this.cdr.markForCheck()
    this.#updateTreeViewBindings(viewType, true)
    this.searchFacade.setSearchExpression(null) // clear search expression
    this.searchFacade.doSearch() // trigger search
    this.reviewFacade.isSwitchingView(false)
  }

  #setInitialView(): void {
    this.reviewFacade.getReviewViewType$.pipe(take(1)).subscribe((viewType) => {
      this.#updateTreeViewBindings(viewType)
    })
  }

  #updateTreeViewBindings(view: ReviewViewType, collapseTree = false): void {
    if (view === ReviewViewType.EmailThread) {
      this.idField.set('__Id')
      this.isEmailThreadView.set(true)
      // if loading for the first time, then collapse the tree
      if (collapseTree) {
        this.expandedKeys = []
        this.reviewFacade.setExpandedEmailThreads([])
      }
      // if exiting viewer, then restore the expanded keys
      else this.#setExpandedKeysFromStore()
    } else {
      this.idField.set('__FileID')
      this.isEmailThreadView.set(false)
    }
  }

  #setExpandedKeysFromStore(): void {
    this.reviewFacade.getExpandedEmailThreads$
      .pipe(take(1))
      .subscribe((keys) => {
        this.expandedKeys = keys
      })
  }

  public getDocTypeIcon(dataItem: any): { iconPath: string; tooltip: string } {
    let iconPath = ''
    let tooltip = ''
    const isArchive =
      !dataItem.__isedoc && !dataItem.__isemail && dataItem.__doctype === 'EDOC'
    if (isArchive)
      return {
        iconPath: 'assets/svg/doctype-archive.svg',
        tooltip: 'Archive file',
      }
    switch (dataItem.__doctype) {
      case 'EMAIL':
      case 'DUMMY_EMAIL':
      case 'GENERATED_EMAIL':
        iconPath = 'assets/svg/doctype-email.svg'
        tooltip = 'Email'
        break
      case 'EMAIL_ATTACH':
        iconPath = 'assets/svg/doctype-email-attachment.svg'
        tooltip = 'Email attachment'
        break
      case 'EDOC':
        iconPath = 'assets/svg/doctype-edoc.svg'
        tooltip = 'Edoc'
        break
      case 'EDOC_ATTACH':
        iconPath = 'assets/svg/doctype-edoc-attachment.svg'
        tooltip = 'Edoc attachment'
        break
    }
    return { iconPath, tooltip }
  }

  public getIsReviewedIcon(dataItem: any): {
    iconPath: string
    tooltip: string
  } {
    if (dataItem?.__isReviewed === 'Yes')
      return {
        iconPath: 'assets/svg/icon-document-reviewed.svg',
        tooltip: 'Reviewed',
      }

    return {
      iconPath: 'assets/svg/icon-document-unreviewed.svg',
      tooltip: 'Reviewed',
    }
  }

  /**
   * Used to disable edit icon for generated or missing emails
   * @param {any} dataItem - The data item
   * @returns {boolean} - true if the email is generated or missing, false otherwise
   */
  public isGeneratedOrMissingEmail(dataItem: any): boolean {
    const docType = dataItem['__doctype']?.toUpperCase()
    return docType === 'DUMMY_EMAIL' || docType === 'GENERATED_EMAIL'
  }

  /**
   * Controls checkbox state in  case of email thread view
   * @param {any} dataItem - The data item
   * @param {ColumnBase} column - The column
   * @param {number} columnIndex - The column index
   * @returns {boolean} - true if the row is selected, false otherwise
   */
  public isSelected = (
    dataItem: any,
    column?: ColumnBase,
    columnIndex?: number
  ): boolean => {
    const isselected = this.#isRowSelected(dataItem)
    return !!isselected && +dataItem['__FileID'] > 0
  }

  /**
   * Checks if the row is selected for email thread view.
   * @param {any} dataItem - The data item
   * @returns {boolean} - true if the row is selected, false otherwise
   */
  #isRowSelected(dataItem: any): boolean {
    return this.currentPageSelectedDocuments.find(
      (row) => row.itemKey === +dataItem['__FileID'] && row.itemKey > 0
    )
      ? true
      : false
  }

  /**
   * Handles selection event of custom checkbox in email thread view
   * @param {any} e - The event
   * @param {any} dataItem - The data item
   */
  public itemSelected(e: any, dataItem: any): void {
    const checked = e.currentTarget.checked
    if (checked) {
      this.currentPageSelectedDocuments.push({
        itemKey: +dataItem['__FileID'],
      })
    } else {
      this.currentPageSelectedDocuments =
        this.currentPageSelectedDocuments.filter(
          (row) => row.itemKey !== +dataItem['__FileID']
        )
    }
    this.#handleSelectionChange(checked ? 'add' : 'remove', [{ dataItem }])
    this.cdr.detectChanges()
  }

  /**
   * Stores the expanded/collapsed email threads keys in the store so that it can be restored when exiting viewer
   * @param {any[]} ids - keys of the expanded email threads
   * @returns {void}
   */
  public onExpandedIdsChange(ids: any[]): void {
    this.reviewFacade.setExpandedEmailThreads(ids)
  }

  public async onSelectionChange(e): Promise<void> {
    this.#handleSelectionChange(e.action, e.items)
  }

  async #handleSelectionChange(
    action: string,
    items: SelectionChangeItem[]
  ): Promise<void> {
    const allFileIds = await firstValueFrom(
      this.searchResultFacade.getSearchResultFileIds
    )
    const changedFileIds = items
      .map((item) => +item.dataItem['__FileID'])
      .filter((id) => id > 0)

    // if selection is done by row click, then clear the previous selection and select the current row
    if (action === 'select') {
      const allPositiveFileIds = allFileIds
        .map((fileId) => +fileId)
        .filter((id) => id > 0)
      this.documentsFacade.removeFromSelectedDocuments(allPositiveFileIds)
      this.documentsFacade.addToSelectedDocuments(changedFileIds)
      this.documentsFacade.setCurrentDocument(changedFileIds[0])
    }
    // if selection is done by checkbox click, then add the selected row
    else if (action === 'add') {
      this.documentsFacade.addToSelectedDocuments(changedFileIds)
    }
    // if checkbox is unchecked, then remove the selected row
    else if (action === 'remove') {
      this.documentsFacade.removeFromSelectedDocuments(changedFileIds)
    }
    this.documentsService.updateDocumentSelection$.next()
    this.documentsService.updateDocumentSelectionToReviewPanel$.next()
  }

  private async updateDocumentTableSelection(): Promise<void> {
    const isBatchSelection = await firstValueFrom(
      this.documentsFacade.getIsBatchSelected$
    )
    const allFileIds = await firstValueFrom(
      this.searchResultFacade.getSearchResultFileIds
    )

    if (!isBatchSelection) {
      const selectedFileIds = await firstValueFrom(
        this.documentsFacade.getSelectedDocuments$
      )
      this.currentPageSelectedDocuments = intersection(
        allFileIds,
        selectedFileIds
      ).map((fileId) => ({ itemKey: fileId }))
    } else {
      const unSelectedFileIds = await firstValueFrom(
        this.documentsFacade.getUnselectedDocuments$
      )

      this.currentPageSelectedDocuments = difference(
        allFileIds,
        unSelectedFileIds
      ).map((fileId) => ({ itemKey: fileId }))
    }
    this.cdr.markForCheck()
  }

  private updateSelectionForEmailThreadView(): void {
    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchResultFacade.getSearchResultFileIds,
    ])
      .pipe(take(1))
      .subscribe(
        ([isBatchSelected, selectedFileIds, unSelectedFileIds, allFileIds]) => {
          this.updateDocTableSelection(
            isBatchSelected,
            selectedFileIds,
            unSelectedFileIds,
            allFileIds
          )
        }
      )
  }

  private updateDocTableSelection(
    isBatchSelection: boolean,
    selectedFileIds: number[],
    unSelectedFileIds: number[],
    allFileIds: number[]
  ): void {
    this.cdr.markForCheck()
    if (!isBatchSelection) {
      this.currentPageSelectedDocuments = intersection(
        allFileIds,
        selectedFileIds
      ).map((fileId) => ({ itemKey: fileId }))
    } else {
      this.currentPageSelectedDocuments = difference(
        allFileIds,
        unSelectedFileIds
      ).map((fileId) => ({ itemKey: fileId }))
    }
  }

  public pageChanged(args: PageArgs): void {
    if (this.currentPage !== args.pageNumber) {
      // If batch is selected, then we need to navigate the document in the review popout window
      this.isDocumentNavigationNeeded = this.isBatchSelected
      this.searchFacade.IsSearchLoading(true)
      this.currentPage = args.pageNumber
      // Since the paging being used in the document table fetches,
      // we need to store the paging and fetch the document table page
      this.searchResultFacade.storeDocumentTablePaging(
        args.pageNumber,
        args.pageSize
      )

      // Once set, fetch the document table page
      this.searchResultFacade.fetchDocumentTablePage()
    }
  }

  public pageSizeChanged(args: PageArgs): void {
    this.isDocumentNavigationNeeded = true
    this.currentPage = args.pageNumber
    this.searchFacade.IsSearchLoading(true)
    this.pageSize = args.pageSize
    // Since the paging being used in the document table fetches,
    // we need to store the paging and fetch the document table page
    this.searchResultFacade.storeDocumentTablePaging(
      args.pageNumber,
      args.pageSize
    )

    // Once set, fetch the document table page
    this.searchResultFacade.fetchDocumentTablePage()
  }

  public showDocumentViewer(rowItem: any): void {
    this.#navigateToDocumentViewer(rowItem.FileID)
  }

  #navigateToDocumentViewer(fileId: number): void {
    this.documentsFacade.updateIsBulkDocument(false)
    this.documentsFacade.setIsBatchSelection(false)
    this.documentsFacade.setCurrentDocument(fileId)
    this.documentsFacade.setSelectedDocuments([fileId])
    this.reviewPanelViewState.setIsNavigatedFromViewer(false)
    this.router.navigate(['/document-detail'], {
      queryParams: {
        projectId: this.projectId,
        docShareToken: this.docShareToken,
        moduleData: this.moduleData,
        reviewSetId: this.reviewSetState.reviewSetId(),
      },
    })
  }

  public resolveTagConfllict(rowItem: any): void {
    this.documentTagService
      .getConflictedTagRules$(this.projectId, rowItem.FileID)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: ResponseModel) => {
        if (response?.data?.length > 0) this.#openBulkTagCoding()
        else this.#showResolveConflictNotification()
      })
  }

  #openBulkTagCoding(): void {
    import(
      '../../document-utility/bulk-tag-coding-action/bulk-tag-coding-aciton-dialog/bulk-tag-coding-aciton-dialog.component'
    ).then((td) => {
      const dialogRef = this.dialogService.open({
        content: td.BulkTagCodingAcitonDialogComponent,
        maxWidth: '1100px',
        maxHeight: '650px',
        width: '80%',
        height: '90vh',
      })

      const bulkTagCoding = dialogRef.content.instance
      bulkTagCoding.isConflictDialog = true
    })
  }

  #showResolveConflictNotification(): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(notificationDialogRef.content.instance)
  }

  #setDialogInput(instance: NotificationDialogComponent): void {
    instance.title = 'Resolve conflict'
    instance.message = `No tag rule conflicts found.`
  }

  public getTagInfo(tag: string): { name: string; color: string } {
    const [name, color] = tag.split('#')
    return { name, color: `#${color}` }
  }

  public openTallyDialog(fieldName: string): void {
    import(
      '../../document-utility/tally/document-tally-dialog/document-tally-dialog.component'
    ).then((td) => {
      const dialogRef = this.dialogService.open({
        content: td.DocumentTallyDialogComponent,
        maxHeight: '90vh',
        maxWidth: '90vw',
        minHeight: '90vh',
        minWidth: '90vw',
      })

      const tallyInfo = dialogRef.content.instance
      tallyInfo.fieldName = fieldName
    })
  }

  public setCurrentDocument(rowItem): void {
    this.documentsFacade.setCurrentDocumentDetails(
      rowItem.FileID,
      rowItem.__filename
    )
  }

  #updateModifiedRowData(data): void {
    data.forEach((item) => {
      const selectedRow = this.tableData().find(
        (row) => +row.__FileID === item.fileId
      )
      if (selectedRow) selectedRow[item.displayFieldName] = item.fieldValue
    })
    this.documentsFacade.resetDocumentState('documentTableUpdatingData')
    this.cdr.markForCheck()
  }

  #updateDocumentTableUI(): void {
    this.documentsFacade.getDocumentTableUpdatingData$
      .pipe(distinctUntilChanged(), takeUntil(this.toDestroy$))
      .subscribe((data) => {
        if (!data) return
        this.#updateModifiedRowData(data)
      })
  }

  #getSearchResponseData(): void {
    combineLatest([
      this.searchFacade.getSearchResponse$,
      this.searchResultFacade.getAllSearchResults,
    ])
      .pipe(
        filter(
          ([searchResponse, searchResults]) =>
            Boolean(searchResponse) && Boolean(searchResults)
        ),
        distinctUntilChanged(
          (prev, curr) => isEqual(prev[0], curr[0]) && isEqual(prev[1], curr[1])
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([searchResponse, searchResults]) => {
        this.searchResponse = searchResponse
        this.searchResults = searchResults
      })
  }

  #selectResetDocumentSelection(): void {
    this.documentsService.resetDocumentSelection$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        if (!this.isReviewPanelPopout) return
        this.documentsFacade.resetDocumentState('selectedDocuments')
        this.isNavigatedFromViewer = false
        this.isDocumentNavigationNeeded = true
        this.#resetDocumentPageSelection()
      })
  }

  #getSelectedDocuments(): void {
    this.documentsFacade.getSelectedDocuments$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((selectedDocuments) => {
        this.currentFileId = selectedDocuments[selectedDocuments.length - 1]
        if (this.currentFileId)
          this.#updateDocumentPageSelection(this.currentFileId)
      })
  }

  #getUserRights(): void {
    combineLatest([
      this.startupsFacade.getProjectGroups$,
      this.startupsFacade.getUserRights$,
    ])
      .pipe(
        filter(
          ([projectGroups, userRights]) =>
            Boolean(projectGroups) && Boolean(userRights)
        ),
        distinctUntilChanged(
          (prev, curr) => isEqual(prev[0], curr[0]) && isEqual(prev[1], curr[1])
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([projectGroups, userRights]) => {
        const payload: WindowMessageContent = {
          type: WindowMessageType.DATA_TRANSFER,
          content: {
            projectGroups: projectGroups,
            userRights: userRights,
            userSelectedLayout: this.layoutState.userSelectedLayout(),
          },
        }
        this.#notifyPopoutWindowDataTransfer(
          AppIdentitiesTypes.REVIEW_PANEL,
          payload
        )
      })
  }

  #getPermittedFields(): void {
    this.fieldFacade.selectAllPermittedFieldsOfCurrentUser$
      .pipe(
        filter((permittedFields) => Boolean(permittedFields)),
        distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((permittedFields) => {
        if (!permittedFields?.[0]) return
        const payload: WindowMessageContent = {
          type: WindowMessageType.DATA_TRANSFER,
          content: {
            permittedFields: permittedFields,
          },
        }
        this.#notifyPopoutWindowDataTransfer(
          AppIdentitiesTypes.REVIEW_PANEL,
          payload
        )
      })
  }

  /**
   * Synchronizes the document selection between the main window and the review pop-out panel.
   * Determines the appropriate file ID and updates the state accordingly.
   *
   * @returns {void}
   */
  #synchronizeDocumentSelection(): void {
    if (!this.isReviewPanelPopout || !this.isDocumentNavigationNeeded) return

    const tableData = this.tableData()

    // Case change or direct document navigation in the main document table.
    let fileId
    // If batch is selected and document navigate through parent window page control
    if (this.isBatchSelected && !this.isPopoutWindowNavigation) {
      const lastRecordNo = tableData.length - 1
      fileId = tableData[lastRecordNo]?.FileID
    } else
      fileId = this.isPopoutWindowNavigation
        ? this.#getFileIdFromPopoutNavigation()
        : this.isNavigatedFromViewer
        ? this.currentFileId
        : tableData[0]?.FileID
    this.updateDocumentSelection(fileId)
  }

  /**
   * Retrieves the file ID when navigating from the pop-out review panel.
   * Handles scenarios like navigation via input or pagination controls.
   *
   * @returns {number} The file Id to navigate to.
   */
  #getFileIdFromPopoutNavigation(): number {
    if (
      this.userPageControlAction === PageControlActionType.PREV_PAGE ||
      this.userPageControlAction === PageControlActionType.LAST_PAGE
    ) {
      const tableData = this.tableData()
      // Use the last file ID in the table for 'Previous' or 'Last' page navigation.
      return tableData[tableData.length - 1]?.FileID
    }

    // Default to the current file ID for other navigation actions.
    return this.currentFileId
  }

  /**
   * Updates document selection and notifies related components.
   * @param {number} fileId - The ID of the document to select
   *
   * @returns {void}
   */
  private updateDocumentSelection(fileId: number): void {
    if (!fileId) return
    this.#notifyProjectChange(fileId)
    this.#updateDocumentStates(fileId)
    this.#updateDocumentPageSelection(fileId)
    this.documentsService.updateDocumentSelectionToReviewPanel$.next()
    this.userPageControlAction = undefined
    this.isPopoutWindowNavigation = false
    this.isDocumentNavigationNeeded = true
    this.cdr.markForCheck()
  }

  /**
   * Updates both current and selected document states.
   *
   * @private
   * @param {number} fileID - File ID to update
   * @returns {void}
   */
  #updateDocumentStates(fileID: number): void {
    this.documentsFacade.setCurrentDocument(fileID)
    this.documentsFacade.setSelectedDocuments([fileID])
    this.documentsService.updateDocumentSelection$.next()
    this.cdr.markForCheck()
  }

  #updateDocumentPageSelection(
    fileId?: number,
    unselectedDocuments?: number[]
  ): void {
    if (unselectedDocuments) {
      for (const key of this.selectedDocumentPageMap.keys()) {
        if (!unselectedDocuments.includes(key)) {
          this.selectedDocumentPageMap.delete(key)
        }
      }
    }

    if (this.selectedDocumentPageMap.has(fileId) || !this.currentPage) return
    this.selectedDocumentPageMap.set(fileId, this.currentPage)
  }

  #resetDocumentPageSelection(): void {
    this.selectedDocumentPageMap.clear()
  }

  #findFileIndex(fileId: number): number {
    // Find file index using more efficient findIndex method
    const fileIndex: number = this.tableData()?.findIndex(
      (item) => item.FileID === fileId
    )
    return fileIndex
  }

  /**
   * receive data from the review popup window
   * @returns {void} This method does not return anything.
   */
  #handleReviewPopoutPanelData(): void {
    this.messengerService.messageReceived
      .pipe(
        filter(
          (message: WindowMessage) =>
            Boolean(message?.payload?.type) &&
            this.isReviewPanelPopout &&
            (message.payload.type === WindowMessageType.FILEID_CHANGED ||
              message.payload.type === WindowMessageType.REVIEW_PANEL_CLOSED)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: WindowMessage) => {
        this.cdr.markForCheck()
        switch (message?.payload?.type) {
          case WindowMessageType.REVIEW_PANEL_CLOSED: {
            // If there is no selected documents, then navigate to document viewer with default value
            const fileId = this.currentFileId ?? -1
            this.#reviewPanelWindowUnloadHandler(message.payload)
            this.#navigateToDocumentViewer(fileId)
            break
          }
          case WindowMessageType.FILEID_CHANGED:
            this.#handleFileIDChangedFromPopupWindow(message)
            break
        }
      })
  }

  /** Handles the event when the review panel is closed.
   * @param {WindowMessage} content The content of the message.
   * @returns {void} This method does not return anything.
   */
  #reviewPanelWindowUnloadHandler(content?: WindowMessageContent): void {
    this.#resetReviewPanelPopoutStatus()
    this.windowManager.removeWindow(AppIdentitiesTypes.REVIEW_PANEL)
    if (content)
      this.utilityPanelStoreUpdateService.updateStoreDataFromParentWindow(
        content
      )
  }

  #resetReviewPanelPopoutStatus(): void {
    LocalStorage.set('isReviewPanelPopout', false)
    LocalStorage.set('isUtilityPanelDataSynced', false)
  }

  #notifyDocumentChangeToReviewPopoutPanel(): void {
    this.documentsService.updateDocumentSelectionToReviewPanel$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        const documentNo = this.currentDocumentSearchScope?.documentNo
        // If the documentNo of out of search scope is same as currentFileID, then no need to notify
        if (this.currentFileId === documentNo) return
        const selectedDocumentPageNo = this.selectedDocumentPageMap.get(
          this.currentFileId
        )
        const payload: WindowMessageContent = {
          type: WindowMessageType.FILEID_CHANGED,
          content: {
            fileId: this.currentFileId,
            currentPageNumber: this.currentPage,
            currentDocumentSearchScope: this.currentDocumentSearchScope,
            isBatchSelected: this.isBatchSelected,
            searchResponse: this.searchResponse,
            searchResults: this.searchResults,
            selectedDocumentPageNo: selectedDocumentPageNo,
            pageSize: this.pageSize,
          },
        }
        this.#notifyPopoutWindowDataTransfer(
          AppIdentitiesTypes.REVIEW_PANEL,
          payload
        )
      })
  }

  #notifyProjectChange(fileId: number): void {
    if (this.previousProjectId === this.projectId) return
    this.previousProjectId = this.projectId
    const payload: WindowMessageContent = {
      type: WindowMessageType.ROUTE_CHANGED,
      content: {
        projectId: this.projectId,
        fileId: fileId,
      },
    }
    this.#notifyPopoutWindowDataTransfer(
      AppIdentitiesTypes.REVIEW_PANEL,
      payload
    )
  }

  /** Handles the event when the file id changed in popout window.
   * @param {WindowMessage} message The content of the message.
   * @returns {void} This method does not return anything.
   */
  #handleFileIDChangedFromPopupWindow(message: WindowMessage): void {
    const {
      fileId,
      userPageControlAction,
      currentDocumentSearchScope,
      currentDocumentTablePage,
    } = message.payload.content
    if (fileId < 0) return

    const fileIndex = this.#findFileIndex(fileId)

    // File is changed in the popout window
    if (this.currentFileId > 0 && this.currentFileId !== fileId) {
      this.userPageControlAction = userPageControlAction
      this.currentFileId = fileId
      this.currentDocumentSearchScope = currentDocumentSearchScope

      if (fileIndex < 0) {
        this.isPopoutWindowNavigation = true
        this.#navigateToPage(currentDocumentTablePage)
      } else this.#updateDocumentStates(fileId)
    } else if (fileIndex > 0 && this.currentFileId !== fileId) {
      // If the user deselects all documents on the current page, the currentFileId becomes null.
      // However, the user still wants to navigate to the next document from the child window.
      this.isPopoutWindowNavigation = true
      this.#updateDocumentStates(fileId)
    }
  }

  #navigateToPage(pageNumber: number): void {
    this.searchResultFacade.storeDocumentTablePaging(pageNumber, this.pageSize)
    this.searchResultFacade.fetchDocumentTablePage()
    this.isDocumentNavigationNeeded = true
  }

  /**
   * Generic method to transfer data between windows
   * @param {payload} Object containing the data to be transferred
   * @param {WindowMessageContent} Type of window content to be sent
   * @returns {void} This method does not return anything.
   */

  #notifyPopoutWindowDataTransfer(
    appType: AppIdentitiesTypes,
    payload: WindowMessageContent
  ): void {
    const popoutWindow = this.windowManager.getWindow(appType)
    if (!popoutWindow) {
      this.#resetReviewPanelPopoutStatus()
      return
    }
    // Send message using messenger service
    this.messengerService.sendMessage(
      {
        payload,
      },
      popoutWindow
    )
  }
}
