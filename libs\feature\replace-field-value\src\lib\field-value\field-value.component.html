<div
  *ngIf="isDuplicateFieldSelected()"
  class="t-flex t-justify-between t-items-center t-bg-[#ED7428] t-p-3">
  <span>{{ duplicateFieldMessage }}</span>
</div>
<div id="replace-form" [formGroup]="replaceForm">
  <ng-container #replaceFormArray formArrayName="replaceFields">
    <ng-container
      *ngFor="let replaceField of replaceFields.controls; index as i">
      <div
        class="t-flex t-flex-row t-m-2 t-p-4 t-bg-[#F7F7F7] t-bg-no-repeat t-rounded-sm t-opacity-100"
        [formGroup]="replaceField">
        <div class="t-flex t-flex-row">
          <div
            kendoTooltip
            class="!t-w-[12.5rem]"
            [tooltipTemplate]="template"
            filter="li.k-list-item">
            <kendo-dropdownlist
              id="field-to-update"
              class=""
              [data]="fieldToUpdate"
              [loading]="isCustomFieldsLoading$ | async"
              textField="displayName"
              valueField="fieldName"
              data-qa="fieldtoupdate"
              formControlName="fieldToUpdate"
              change="onFieldChange($event, i)"
              [filterable]="true"
              [virtual]="{ itemHeight: 28 }"
              [kendoDropDownFilter]="{
                caseSensitive: false,
                operator: 'contains'
              }"
              [popupSettings]="{ width: 225, appendTo: 'component' }">
            </kendo-dropdownlist>
          </div>
        </div>

        <div class="t-flex">
          <div
            class="t-bg-green-200 t-w-10 custom-svg-line t-bg-no-repeat t-bg-center"></div>
        </div>

        <div class="t-flex t-flex-col t-w-1/4">
          <div class="t-text-left t-mb-1">
            <kendo-label class="t-text-sm t-font-medium"
              >Action<span class="t-text-sm t-text-[#ED7428]">
                *</span
              ></kendo-label
            >
          </div>
          <div class="t-flex t-flex-col t-ml-1">
            <div class="t-flex t-mt-1 t-items-center">
              <input
                [id]="'replace-entire-field-' + i"
                size="small"
                type="radio"
                value="REPLACE_ENTIRE_FIELD"
                data-qa="replaceentirefield"
                formControlName="replaceAction"
                kendoRadioButton />
              <kendo-label
                [for]="'replace-entire-field-' + i"
                text="Replace Entire Field"
                class="t-k-radio-label t-px-[5px] t-relative"
                [ngClass]="{
                  't-text-[#2F3080] t-font-medium t-text-[14px]':
                    replaceField.get('replaceAction').value ===
                    'REPLACE_ENTIRE_FIELD'
                }"></kendo-label>
            </div>
            <div class="t-flex t-mt-1 t-items-center">
              <input
                [id]="'append-to-end' + i"
                [attr.disabled]="isTextField(i) === false ? true : null"
                size="small"
                type="radio"
                value="APPEND_TO_END"
                data-qa="apendtoend"
                formControlName="replaceAction"
                kendoRadioButton />
              <kendo-label
                [for]="'append-to-end' + i"
                text="Append to End"
                class="t-k-radio-label t-px-[5px] t-relative"
                [ngClass]="{
                  't-text-[#2F3080] t-font-medium t-text-[14px]':
                    replaceField.get('replaceAction').value === 'APPEND_TO_END'
                }"></kendo-label>
            </div>
            <div
              *ngIf="showDelimiter(i, replaceActionType.APPEND_TO_END)"
              class="t-flex t-flex-row t-items-center t-pl-5 t-my-1">
              <kendo-dropdownlist
                class="!t-w-28"
                id="delimiter"
                [data]="delimiterList"
                formControlName="indexDelimiter"
                textField="displayText"
                valueField="indexDelimiter"
                [loading]="isCustomFieldsLoading$ | async"
                [valuePrimitive]="false"
                [filterable]="true"
                [virtual]="{ itemHeight: 28 }"
                [popupSettings]="{
                  width: 200,
                  height: 150
                }"
                [kendoDropDownFilter]="{
                  caseSensitive: true,
                  operator: 'contains'
                }"
                data-qa="delimiter">
              </kendo-dropdownlist>
            </div>
            <div class="t-flex t-mt-1 t-items-center">
              <input
                [id]="'insert-at-begining' + i"
                [attr.disabled]="isTextField(i) === false ? true : null"
                size="small"
                type="radio"
                value="INSERT_AT_BEGINING"
                data-qa="insertatbegining"
                formControlName="replaceAction"
                kendoRadioButton />
              <kendo-label
                [for]="'insert-at-begining' + i"
                text="Insert at Begining"
                class="t-k-radio-label t-px-[5px] t-relative"
                [ngClass]="{
                  't-text-[#2F3080] t-font-medium t-text-[14px]':
                    replaceField.get('replaceAction').value ===
                    'INSERT_AT_BEGINING'
                }"></kendo-label>
            </div>
            <div
              *ngIf="showDelimiter(i, replaceActionType.INSERT_AT_BEGINING)"
              class="t-flex t-flex-row t-items-center t-pl-5 t-my-1">
              <kendo-dropdownlist
                class="!t-w-28"
                id="delimiter"
                [data]="delimiterList"
                formControlName="indexDelimiter"
                textField="displayText"
                valueField="indexDelimiter"
                [loading]="isCustomFieldsLoading$ | async"
                [valuePrimitive]="false"
                [filterable]="true"
                [virtual]="{ itemHeight: 28 }"
                [popupSettings]="{
                  width: 200,
                  height: 150
                }"
                [kendoDropDownFilter]="{
                  caseSensitive: true,
                  operator: 'contains'
                }"
                data-qa="delimiter">
              </kendo-dropdownlist>
            </div>
            <div class="t-flex t-mt-1 t-items-center">
              <input
                [id]="'search-for' + i"
                size="small"
                type="radio"
                value="SEARCH_FOR"
                data-qa="searchfor"
                formControlName="replaceAction"
                kendoRadioButton />
              <kendo-label
                [for]="'search-for' + i"
                text="Search For"
                class="t-k-radio-label t-px-[5px] t-relative"
                [ngClass]="{
                  't-text-[#2F3080] t-font-medium t-text-[14px]':
                    replaceField.get('replaceAction').value === 'SEARCH_FOR'
                }"></kendo-label>
            </div>
            <div
              *ngIf="showSearchText(i)"
              class="t-flex t-flex-row t-items-center t-pl-5 t-mt-1">
              <div class="t-w-[90%]">
                <div *ngIf="isCustomFieldsLoading$ | async">
                  <kendo-textarea
                    placeholder="text"
                    data-qa="dummyInput"
                    [rows]="1"></kendo-textarea>
                </div>
                <div *ngIf="checkDataType(i, uiType.Numeric)">
                  <kendo-numerictextbox
                    placeholder="0"
                    format="#.######"
                    [decimals]="36"
                    data-qa="searchTextNumeric"
                    formControlName="searchText"></kendo-numerictextbox>
                </div>
                <div
                  *ngIf="
                    checkDataType(i, uiType.Text) ||
                    checkDataType(i, uiType.UnicodeText) ||
                    checkDataType(i, uiType.UnicodeParagraph) ||
                    checkDataType(i, uiType.Paragraph)
                  ">
                  <kendo-textarea
                    placeholder="text"
                    data-qa="searchText"
                    formControlName="searchText"
                    [rows]="1"
                    resizable="vertical"></kendo-textarea>
                </div>
                <div *ngIf="checkDataType(i, uiType.Boolean)">
                  <kendo-dropdownlist
                    [data]="booleanList"
                    placeholder="text"
                    data-qa="searchTextBoolean"
                    formControlName="searchText"></kendo-dropdownlist>
                </div>
                <div
                  class="t-flex t-flex-row t-flex-wrap t-gap-2 t-items-center"
                  *ngIf="
                    checkDataType(i, uiType.DateTime) ||
                    checkDataType(i, uiType.Date)
                  ">
                  <kendo-datepicker
                    placeholder="Choose a date ..."
                    [format]="'M/d/yyyy'"
                    data-qa="searchTextDate"
                    formControlName="searchText"></kendo-datepicker>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="t-flex">
          <div
            class="t-bg-green-200 t-w-10 custom-svg-line t-bg-no-repeat t-bg-center"></div>
        </div>

        <div class="t-flex t-flex-col t-w-1/4">
          <div class="t-text-left t-pb-2">
            <kendo-label class="t-text-sm t-font-medium"
              >Update With<span class="t-text-sm t-text-[#ED7428]">
                *</span
              ></kendo-label
            >
          </div>
          <div class="t-flex t-flex-col t-ml-1">
            <div class="t-flex t-mt-1 t-items-center">
              <input
                [id]="'update-with-text' + i"
                size="small"
                type="radio"
                value="UPDATE_WITH_TEXT"
                formControlName="updateWithType"
                data-qa="updatewithtext"
                kendoRadioButton />
              <kendo-label
                class="t-font-semibold"
                [for]="'update-with-text' + i"
                class="t-k-radio-label t-px-[5px] t-relative"
                [ngClass]="{
                  't-text-[#2F3080] t-font-medium t-text-[14px]':
                    replaceField.get('updateWithType').value ===
                    'UPDATE_WITH_TEXT'
                }"
                text="Text"></kendo-label>
            </div>
            <div
              *ngIf="isUpdateWithText(i)"
              class="t-flex t-flex-row t-items-center t-pl-5 t-my-1">
              <div class="t-w-[90%]">
                <div *ngIf="isCustomFieldsLoading$ | async">
                  <kendo-textarea
                    placeholder="text"
                    data-qa="dummyInput"
                    [rows]="1"></kendo-textarea>
                </div>
                <div *ngIf="checkDataType(i, uiType.Numeric)">
                  <kendo-numerictextbox
                    placeholder="0"
                    format="#.######"
                    [decimals]="36"
                    data-qa="updatewithtextvalueNumeric"
                    formControlName="text"></kendo-numerictextbox>
                </div>
                <div
                  *ngIf="
                    checkDataType(i, uiType.Text) ||
                    checkDataType(i, uiType.UnicodeText) ||
                    checkDataType(i, uiType.UnicodeParagraph) ||
                    checkDataType(i, uiType.Paragraph)
                  ">
                  <kendo-textarea
                    placeholder="text"
                    data-qa="updatewithtextvalueText"
                    formControlName="text"
                    [rows]="1"
                    resizable="vertical"></kendo-textarea>
                </div>
                <div *ngIf="checkDataType(i, uiType.Boolean)">
                  <kendo-dropdownlist
                    [data]="booleanList"
                    placeholder=""
                    data-qa="updatewithtextvalueBoolean"
                    formControlName="text"></kendo-dropdownlist>
                </div>
                <div
                  class="t-flex t-flex-row t-flex-wrap t-gap-3 t-items-center"
                  *ngIf="checkDataType(i, uiType.Date)">
                  <kendo-datepicker
                    [format]="'M/d/yyyy'"
                    placeholder="Choose a date ..."
                    data-qa="updatewithtextvalueDate"
                    formControlName="text"></kendo-datepicker>
                </div>
                <div
                  class="t-flex t-flex-row t-flex-wrap t-gap-3 t-items-center"
                  *ngIf="checkDataType(i, uiType.DateTime)">
                  <kendo-datetimepicker
                    placeholder="Choose date and time ..."
                    position="top center"
                    format="M/d/yyyy h:mm:ss a"
                    formControlName="dateTimeText"
                    calendarType="infinite"
                    [popupSettings]="popupSettings"
                    data-qa="updatewithtextvalueDate"></kendo-datetimepicker>
                </div>
              </div>
            </div>
            <div class="t-flex t-mt-1 t-items-center">
              <input
                [id]="'update-with-field' + i"
                size="small"
                type="radio"
                value="UPDATE_WITH_FIELD"
                formControlName="updateWithType"
                data-qa="updatewithfield"
                kendoRadioButton />
              <kendo-label
                class="t-font-semibold"
                [for]="'update-with-field' + i"
                class="t-k-radio-label t-px-[5px] t-relative"
                [ngClass]="{
                  't-text-[#2F3080] t-font-medium t-text-[14px]':
                    replaceField.get('updateWithType').value ===
                    'UPDATE_WITH_FIELD'
                }"
                text="Field"></kendo-label>
            </div>

            <div
              *ngIf="!isUpdateWithText(i)"
              class="t-flex t-flex-row t-items-center t-pl-5 t-mt-1">
              <div
                kendoTooltip
                class="t-w-[90%]"
                [tooltipTemplate]="template"
                filter="li.k-list-item">
                <kendo-dropdownlist
                  class="!t-min-w-[14rem]"
                  [data]="getFieldToUpdateWithList(i)"
                  textField="displayFieldName"
                  valueField="id"
                  data-qa="updatewithfieldname"
                  formControlName="updateWith"
                  [filterable]="true"
                  [virtual]="{ itemHeight: 28 }"
                  [popupSettings]="{ width: 250, appendTo: 'component' }"
                  [kendoDropDownFilter]="{
                    caseSensitive: false,
                    operator: 'contains'
                  }">
                </kendo-dropdownlist>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>
</div>

<div class="t-mt-3 t-mx-2 t-flex t-gap-4 t-justify-end">
  <button
    kendoButton
    id="add-field"
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    data-qa="add"
    (click)="addReplaceForm()">
    ADD FIELD
  </button>
  <button
    kendoButton
    *ngIf="showRemoveButton()"
    id="remove-field"
    class="v-custom-secondary-button"
    themeColor="error"
    fillMode="outline"
    data-qa="remove"
    (click)="removeReplaceForm()">
    REMOVE FIELD
  </button>
</div>

<!-- for tooltip in dropdownlist -->
<ng-template #template let-anchor>
  <span>{{ anchor.nativeElement.innerText }}</span>
</ng-template>
