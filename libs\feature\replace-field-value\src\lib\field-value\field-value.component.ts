import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { LabelModule } from '@progress/kendo-angular-label'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import {
  NumericTextBoxModule,
  RadioButtonModule,
  TextAreaModule,
  TextBoxModule,
} from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import {
  CodingOverlaySaveModel,
  Field,
  FindReplacePayloadModel,
  OverlayCustomFieldsDelimiterModel,
  OverlayCustomFieldsInputTypes,
  ReplaceActionType,
  ReplaceFieldFacade,
  UpdateWithType,
} from '@venio/data-access/review'
import {
  Observable,
  Subject,
  combineLatest,
  debounceTime,
  filter,
  takeUntil,
} from 'rxjs'
import { DocumentCodingViewModel } from '@venio/shared/models/interfaces'
import {
  DateInputsModule,
  DatePickerModule,
  DateTimePickerModule,
  FormatSettings,
  PopupSettings,
} from '@progress/kendo-angular-dateinputs'
import { formatDate } from '@progress/kendo-angular-intl'
import { ActivatedRoute } from '@angular/router'
import { VenioNotificationService } from '@venio/feature/notification'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-field-value',
  standalone: true,
  imports: [
    CommonModule,
    LabelModule,
    DropDownsModule,
    FormsModule,
    ReactiveFormsModule,
    RadioButtonModule,
    TextBoxModule,
    TextAreaModule,
    ButtonsModule,
    DialogModule,
    DateInputsModule,
    DatePickerModule,
    DateTimePickerModule,
    NumericTextBoxModule,
    TooltipsModule,
  ],
  templateUrl: './field-value.component.html',
  styleUrl: './field-value.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FieldValueComponent implements OnInit, AfterViewInit, OnDestroy {
  public replaceForm!: FormGroup

  public readonly toDestroy$ = new Subject<void>()

  public fieldToUpdate: DocumentCodingViewModel[] = []

  public fieldToUpdateWith: Field[] = []

  public delimiterList: OverlayCustomFieldsDelimiterModel[] = []

  public isCustomFieldsLoading$: Observable<boolean>

  public isReplaceInProgress$ = this.replaceFieldFacade.isReplaceInProgress$

  public isDisableAppendRadio = true

  public defaultDateTime: Date = formatDate(new Date(), 'M/d/yyyy h:mm:ss')

  public defaultDate: Date = formatDate(new Date(), 'M/d/yyyy')

  public format: FormatSettings = {
    displayFormat: 'dd/MM/yyyy HH:mm',
    inputFormat: 'dd/MM/yy HH:mm',
  }

  public booleanList = ['True', 'False']

  public duplicateFieldMessage = 'Cannot bulk replace duplicate fields.'

  public readonly uiType = OverlayCustomFieldsInputTypes

  public readonly replaceActionType = ReplaceActionType

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  @ViewChild('replaceFormArray', { read: ViewContainerRef })
  public dateTimePickerPopup: ViewContainerRef

  public popupSettings: PopupSettings = null

  constructor(
    private fb: FormBuilder,
    public dialog: DialogRef,
    public cdr: ChangeDetectorRef,
    public activatedRoute: ActivatedRoute,
    private replaceFieldFacade: ReplaceFieldFacade,
    private notificationService: VenioNotificationService
  ) {}

  public ngAfterViewInit(): void {
    this.popupSettings = {
      appendTo: this.dateTimePickerPopup,
      animate: true,
      popupClass: '',
    }
  }

  public ngOnInit(): void {
    this.selectMergeResponse()
    this.fieldLoadingHandling()
    this.initForm()
    this.getFields()
  }

  private selectMergeResponse(): void {
    this.replaceFieldFacade.selectReplacesResponse$
      .pipe(
        debounceTime(100),
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.cdr.markForCheck()
        if (response.status === 'success') {
          if (response?.data?.replaceType === 'find-replace-field') {
            const responseData = response?.data
              ?.responseData as CodingOverlaySaveModel[]
            if (Array.isArray(responseData) && responseData.length > 0) {
              const isAllReplaceSuccess = responseData?.every(
                (element) => element.isSuccess
              )
              if (isAllReplaceSuccess) {
                this.notificationService.showSuccess(response.message)
                this.resetForm()
              } else {
                responseData.forEach((element) => {
                  if (element.isSuccess === false) {
                    this.notificationService.showError(
                      `'${element.displayFieldName}': ${element.message}`
                    )
                  } else {
                    this.notificationService.showSuccess(
                      `'${element.displayFieldName}': ${element.message}`
                    )
                  }
                })
              }
            }
          }
        } else {
          this.notificationService.showError(response.message)
        }
        this.replaceFieldFacade.clearReplaceReponse()
      })
  }

  public resetForm(): void {
    this.cdr.markForCheck()
    if (this.replaceFields.length > 0) this.replaceFields.controls.splice(0)
    this.addReplaceForm()
  }

  public fieldLoadingHandling(): void {
    this.isCustomFieldsLoading$ = this.replaceFieldFacade.isCustomFieldsLoading$

    this.isReplaceInProgress$ = this.replaceFieldFacade.isReplaceInProgress$

    this.replaceFieldFacade.isReplaceInProgress$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((status) => {
        this.cdr.markForCheck()
        if (this.replaceFields) {
          if (!status) {
            for (const control of this.replaceFields.controls) {
              control.get('fieldToUpdate').enable({ emitEvent: false })
              control.get('replaceAction').enable()
              control.get('indexDelimiter').enable()
              control.get('searchText').enable()
              control.get('updateWithType').enable()
              control.get('text').enable()
              control.get('updateWith').enable()
            }
          } else {
            for (const control of this.replaceFields.controls) {
              control.get('fieldToUpdate').disable({ emitEvent: false })
              control.get('replaceAction').disable()
              control.get('indexDelimiter').disable()
              control.get('searchText').disable()
              control.get('updateWithType').disable()
              control.get('text').disable()
              control.get('updateWith').disable()
            }
          }
        }
      })
  }

  private initForm(): void {
    this.replaceForm = this.fb.group({
      replaceFields: this.fb.array([this.getReplaceFormData(null)]),
    })
  }

  private getFields(): void {
    combineLatest([
      this.replaceFieldFacade.getCustomFields$,
      this.replaceFieldFacade.getComponentFields$,
      this.replaceFieldFacade.getDelimiters$,
    ])
      .pipe(
        debounceTime(100),
        filter((result) => !!result),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([fields, componentField, delimiters]) => {
        this.cdr.markForCheck()
        this.delimiterList = delimiters.map(
          (delimiter, index) =>
            ({
              indexDelimiter: index,
              displayText: delimiter,
            } as OverlayCustomFieldsDelimiterModel)
        )
        this.fieldToUpdate = fields
        this.fieldToUpdateWith = componentField.map((item) =>
          Object.assign({}, item)
        )
        const defaultField = this.getFirstUnselectedField()
        if (defaultField) {
          this.replaceFields.controls[0]
            .get('fieldToUpdate')
            ?.setValue(defaultField, { emitEvent: false })
          this.handleFieldToUpdateChange(0)
          this.updateFieldsAfterFieldChange(0, defaultField)
        }
      })
  }

  private getReplaceFormData(
    fieldToUpdate: DocumentCodingViewModel
  ): FormGroup<any> {
    return this.fb.group({
      fieldToUpdate: [{ value: fieldToUpdate, disabled: false }],
      replaceAction: ReplaceActionType.REPLACE_ENTIRE_FIELD,
      indexDelimiter: null,
      searchText: [''],
      updateWithType: UpdateWithType.UPDATE_WITH_TEXT,
      text: ['', Validators.required],
      dateTimeText: null,
      updateWith: [{ value: null, disabled: false }],
      updateWithList: null,
      uiType: null,
    })
  }

  public addReplaceForm(): void {
    const defaultField = this.getFirstUnselectedField()
    if (defaultField) {
      this.cdr.markForCheck()

      // set the field to update with list
      const replaceForm = this.getReplaceFormData(defaultField)
      this.replaceFields.push(replaceForm)
      const index = this.replaceFields.length - 1
      this.handleFieldToUpdateChange(index)
      this.updateFieldsAfterFieldChange(index, defaultField)
    }
  }

  public handleFieldToUpdateChange(index: number): void {
    this.replaceFields.controls[index]
      .get('fieldToUpdate')
      .valueChanges.pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((field: DocumentCodingViewModel) => {
        this.cdr.markForCheck()
        this.updateFieldsAfterFieldChange(index, field)
      })
  }

  private updateFieldsAfterFieldChange(
    index: number,
    field: DocumentCodingViewModel
  ): void {
    const control = this.replaceFields.controls[index]

    control.get('searchText')?.setValue('')
    control.get('text')?.setValue('')

    const fieldToUpdateWith = this.getFieldsToUpdateWith(
      this.fieldToUpdateWith,
      field
    )
    control.get('updateWithList')?.setValue(fieldToUpdateWith)
    control.get('updateWith')?.setValue(fieldToUpdateWith[0])

    const fieldToUpdate: DocumentCodingViewModel =
      control.get('fieldToUpdate')?.value

    if (fieldToUpdate) {
      const inputType = (
        fieldToUpdate.uiInputType || ''
      ).toLowerCase() as OverlayCustomFieldsInputTypes

      control.get('uiType')?.setValue(inputType)

      if (
        field.allowMultipleCodingValues === true &&
        field.delimiterForCodingValues
      ) {
        const multiValueDelimiters = this.delimiterList.filter(
          (d: OverlayCustomFieldsDelimiterModel) => {
            return d.displayText.includes(field.delimiterForCodingValues)
          }
        )
        const multiValueDelimiter = multiValueDelimiters.length
          ? multiValueDelimiters[0]
          : null
        control.get('indexDelimiter').setValue(multiValueDelimiter)
        control.get('indexDelimiter').disable({ emitEvent: false })
      } else {
        control
          .get('indexDelimiter')
          .setValue({ displayText: 'select', indexDelimiter: 0 })
        control.get('indexDelimiter').enable({ emitEvent: false })
      }

      const isDisableAppendRadio = this.isFieldTypeText(inputType)

      if (!isDisableAppendRadio) {
        const replaceAction =
          this.replaceFields.controls[index].get('replaceAction').value
        if (
          replaceAction === ReplaceActionType.APPEND_TO_END ||
          replaceAction === ReplaceActionType.INSERT_AT_BEGINING
        ) {
          this.replaceFields.controls[index]
            .get('replaceAction')
            ?.setValue(ReplaceActionType.REPLACE_ENTIRE_FIELD)
        }
      }
    }
  }

  private getFieldsToUpdateWith(
    componentFields: Field[],
    selectedCustomField: DocumentCodingViewModel
  ): Field[] {
    const { uiInputType } = selectedCustomField
    const inputType = (
      uiInputType || ''
    ).toLowerCase() as OverlayCustomFieldsInputTypes

    const isText = this.isFieldTypeText(inputType)

    const filteredFields = componentFields.filter(
      (c) =>
        this.getInputType(c.fieldDataType) === inputType &&
        !(
          c.venioFieldId === selectedCustomField.customFieldInfoId &&
          c.isCustomField
        )
    )

    // if the filtered field is the only selected custom field, show none
    if (
      filteredFields.length === 1 &&
      filteredFields[0].venioFieldId ===
        selectedCustomField.customFieldInfoId &&
      filteredFields[0].isCustomField
    ) {
      return []
    }

    return isText ? componentFields : filteredFields
  }

  private isFieldTypeText(inputType: OverlayCustomFieldsInputTypes): boolean {
    return (
      inputType === OverlayCustomFieldsInputTypes.UnicodeText ||
      inputType === OverlayCustomFieldsInputTypes.UnicodeParagraph ||
      inputType === OverlayCustomFieldsInputTypes.Text ||
      inputType === OverlayCustomFieldsInputTypes.Paragraph
    )
  }

  public get replaceFields(): FormArray<FormGroup> {
    return this.replaceForm?.controls['replaceFields'] as FormArray
  }

  public getFirstUnselectedField(): DocumentCodingViewModel {
    return this.fieldToUpdate.find((f) => {
      return !this.replaceFields.controls.some(
        (c) => c.get('fieldToUpdate')?.value?.fieldName === f.fieldName
      )
    })
  }

  public removeReplaceForm(): void {
    if (this.replaceFields.length > 1) {
      this.cdr.markForCheck()
      this.replaceFields.removeAt(this.replaceFields.length - 1)
    }
  }

  public isUpdateWithText(index: number): boolean {
    return (
      this.replaceFields.controls[index].get('updateWithType')?.value ===
      UpdateWithType.UPDATE_WITH_TEXT
    )
  }

  public showDelimiter(index: number, type: ReplaceActionType): boolean {
    const replaceAction =
      this.replaceFields.controls[index].get('replaceAction')?.value

    return (
      (type === ReplaceActionType.APPEND_TO_END &&
        replaceAction === ReplaceActionType.APPEND_TO_END) ||
      (type === ReplaceActionType.INSERT_AT_BEGINING &&
        replaceAction === ReplaceActionType.INSERT_AT_BEGINING)
    )
  }

  public showSearchText(index: number): boolean {
    return (
      this.replaceFields.controls[index].get('replaceAction')?.value ===
      ReplaceActionType.SEARCH_FOR
    )
  }

  public isDuplicateFieldSelected(): boolean {
    const fields = this.replaceFields.controls.map(
      (f) => f.get('fieldToUpdate')?.value?.customFieldInfoId as number
    )
    return new Set(fields).size !== fields.length
  }

  public isTextField(index: number): boolean {
    const fieldToUpdate: DocumentCodingViewModel =
      this.replaceFields.controls[index].get('fieldToUpdate')?.value

    if (!fieldToUpdate) return true

    const inputType = (
      fieldToUpdate.uiInputType || ''
    ).toLowerCase() as OverlayCustomFieldsInputTypes

    const isDisableAppendRadio =
      this.isFieldTypeText(inputType) || fieldToUpdate.allowMultipleCodingValues
    return isDisableAppendRadio
  }

  public getFieldToUpdateWithList(index: number): Field[] {
    return this.replaceFields.controls[index].get('updateWithList')
      ?.value as Field[]
  }

  public validateReplaceForm(payload: FindReplacePayloadModel[]): string {
    for (const p of payload) {
      if (
        p.replaceValueType === ReplaceActionType.APPEND_TO_END ||
        p.replaceValueType === ReplaceActionType.INSERT_AT_BEGINING
      ) {
        if (p.indexDelimeter === undefined || p.indexDelimeter === null) {
          return 'Please select a delimiter'
        }
      }

      if (p.replaceValueType === ReplaceActionType.SEARCH_FOR) {
        if (
          p.findValue === undefined ||
          p.findValue === null ||
          p.findValue === ''
        ) {
          return 'Please enter a search text'
        }
      }

      if (p.replaceValueWith === UpdateWithType.UPDATE_WITH_FIELD) {
        if (p.replaceFieldId === undefined || p.replaceFieldId === null) {
          return 'Please select a field to update with'
        }
      } else {
        if (
          p.replaceValue === undefined ||
          p.replaceValue === null ||
          p.replaceValue === ''
        ) {
          return 'Please enter a replace text'
        }
      }
    }
    return ''
  }

  public onReplaceClicked(): void {
    if (this.isDuplicateFieldSelected()) {
      this.notificationService.showError(this.duplicateFieldMessage)
      return
    }
    const replaceOptions = this.getPayload()
    const errorMessage = this.validateReplaceForm(replaceOptions)
    if (errorMessage !== '') {
      this.notificationService.showError(errorMessage)
      return
    }

    this.replaceFieldFacade.findReplaceField(this.projectId, replaceOptions)
  }

  private getPayload(): FindReplacePayloadModel[] {
    const replaceFields = this.replaceForm.get('replaceFields').getRawValue()

    if (!replaceFields) return []
    return replaceFields.map((field) => {
      return {
        customFieldId: field.fieldToUpdate.customFieldInfoId,
        displayFieldName: field.fieldToUpdate.displayName,
        replaceValueType: field.replaceAction,
        findValue: this.getSearchText(field, field.searchText),
        indexDelimeter: field.indexDelimiter?.['indexDelimiter'],
        replaceValueWith: field.updateWithType,
        replaceValue: this.getSearchText(
          field,
          field.uiType.toLowerCase() === OverlayCustomFieldsInputTypes.DateTime
            ? field.dateTimeText
            : field.text
        ),
        replaceFieldId: field.updateWith?.venioFieldId,
        replaceFieldIsCustomField: field.updateWith?.isCustomField,
      } as FindReplacePayloadModel
    }) as FindReplacePayloadModel[]
  }

  public getSearchText(field: any, text: any): any {
    if (field.uiType.toLowerCase() === OverlayCustomFieldsInputTypes.DateTime) {
      return formatDate(text, 'M/d/yyyy h:mm:ss a')
    } else if (
      field.uiType.toLowerCase() === OverlayCustomFieldsInputTypes.Date
    ) {
      return formatDate(text, 'M/d/yyyy')
    }
    return text
  }

  public onCancel(): void {
    this.dialog.close()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.replaceForm.reset()
  }

  private getInputType = (
    fieldDataType: string
  ): OverlayCustomFieldsInputTypes => {
    let uiInputType = OverlayCustomFieldsInputTypes.UnicodeText
    switch (fieldDataType?.toLowerCase()) {
      case 'int':
      case 'bigint':
      case 'float':
      case 'decimal':
        uiInputType = OverlayCustomFieldsInputTypes.Numeric
        break
      case 'nvarchar':
      case 'varchar':
        uiInputType = OverlayCustomFieldsInputTypes.Text
        break
      case 'bit':
        uiInputType = OverlayCustomFieldsInputTypes.Boolean
        break
      case 'date':
        uiInputType = OverlayCustomFieldsInputTypes.Date
        break
      case 'time':
      case 'datetime':
        uiInputType = OverlayCustomFieldsInputTypes.DateTime
        break
    }

    return uiInputType
  }

  public checkDataType(
    index: number,
    type: OverlayCustomFieldsInputTypes
  ): boolean {
    return type === this.replaceFields.controls[index].get('uiType')?.value
  }

  public showRemoveButton(): boolean {
    return this.replaceFields?.length > 1
  }
}
