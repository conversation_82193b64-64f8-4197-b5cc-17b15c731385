import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import {
  DelSearchHistoryRequestModel,
  ReviewBreadCrumb,
  SaveSearchRequestModel,
  SearchDupOption,
  SearchHistoryRequestModel,
  SearchInputParams,
  SearchResponseModel,
  SearchStatusModel,
} from '../../models/interfaces/search.model'
import * as SearchActions from './search.actions'
import * as SearchSelectors from './search.selectors'
import { BehaviorSubject, catchError, Observable, of, Subject } from 'rxjs'
import { ReviewBreadcrumbType } from '../../models/constants'
import { DynamicFolderModel, FolderModel } from '../../models/interfaces'
import { SearchState } from './search.reducer'
import { SearchService } from '../../services'
import { HttpErrorResponse } from '@angular/common/http'

type SearchStateKeys = keyof SearchState | Array<keyof SearchState>

@Injectable()
export class SearchFacade {
  public filterSubject: Subject<any> = new Subject()

  public exitViewer$: Subject<void> = new Subject()

  public clearSearchResult$: Subject<void> = new Subject<void>()

  // used to trigger search from other component to refresh document list
  public triggerSearch$: BehaviorSubject<boolean | null> = new BehaviorSubject(
    null
  )

  public selectResetSearchInputControl$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState('shouldResetSearchInputControls')
    )
  )

  public selectTagRuleIdPatternList$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('tagRuleIdPatternList'))
  )

  public selectSearchFormValues$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('searchFormValues'))
  )

  // triggers search from other component to refresh document list
  public doSearch(): void {
    this.triggerSearch$.next(true)
    setTimeout(() => {
      this.triggerSearch$.next(null)
    }, 100)
  }

  public getSearchResponse$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('searchResponse'))
  )

  public getIsSearchLoading$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('isSearchLoading'))
  )

  public getSearchTempTables$ = this.store.pipe(
    select(SearchSelectors.getSearchTempTables)
  )

  public getSearchInitialParameters$ = this.store.pipe(
    select(SearchSelectors.getSearchInitialSearchResultParameters)
  )

  public getReviewSetBatchId$ = this.store.pipe(
    select(SearchSelectors.getReviewSetBatchId)
  )

  public getTotalHitCount$ = this.store.pipe(
    select(SearchSelectors.getTotalHitCount)
  )

  public getBreadcrumbs$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('breadcrumbs'))
  )

  public getMainSearchBreadcrumbs$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('mainSearchBreadcrumb'))
  )

  public getConditionSearchBreadcrumbs$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('conditionalBreadcrumbs'))
  )

  public getFilterSearchBreadcrumbs$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('filterBreadcrumbs'))
  )

  constructor(
    private readonly store: Store,
    private searchService: SearchService
  ) {}

  public search(payload: SearchInputParams): void {
    this.store.dispatch(SearchActions.search({ payload }))
  }

  public clearSearchResponse(): void {
    this.store.dispatch(SearchActions.clearSearchResponse())
  }

  public addBreadcrumb(
    breadcrumb: ReviewBreadCrumb,
    breadcrumbType: ReviewBreadcrumbType
  ): void {
    this.store.dispatch(
      SearchActions.addBreadcrumbs({
        breadcrumb: breadcrumb,
        breadcrumbType: breadcrumbType,
      })
    )
  }

  public storeSearchFormValues(searchFormValues: SearchInputParams): void {
    this.store.dispatch(
      SearchActions.storeSearchFormValues({ searchFormValues })
    )
  }

  public updateBreadcrumbs(
    breadcrumbs: ReviewBreadCrumb[],
    breadcrumbType: ReviewBreadcrumbType
  ): void {
    this.store.dispatch(
      SearchActions.updateBreadcrumbs({
        breadcrumbs: breadcrumbs,
        breadcrumbType: breadcrumbType,
      })
    )
  }

  public IsSearchLoading(isSearchLoading: boolean): void {
    this.store.dispatch(
      SearchActions.IsSearchLoadingAction({ isSearchLoading })
    )
  }

  public setDynamicFolderSearchScope(dynamicFolder: DynamicFolderModel): void {
    this.store.dispatch(
      SearchActions.setDynamicFolderSearchScope({ dynamicFolder })
    )
  }

  public setStaticFolderSearchScope(staticFolder: FolderModel): void {
    this.store.dispatch(
      SearchActions.setStaticFolderSearchScope({ staticFolder })
    )
  }

  public getDynamicFolderSearchScope$ = this.store.pipe(
    select(SearchSelectors.getDynamicFolderSearchScope)
  )

  public getStaticFolderSearchScope$ = this.store.pipe(
    select(SearchSelectors.getStaticFolderSearchScope)
  )

  public fetchSearchHistory(
    projectId: number,
    searchHistoryRequestModel: SearchHistoryRequestModel
  ): void {
    this.store.dispatch(
      SearchActions.fetchSearchHistory({ projectId, searchHistoryRequestModel })
    )
  }

  public getSearchHistory$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('searchHistory'))
  )

  public getTotalSearchHistoryCount$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('totalSearchHistoryCount'))
  )

  public getSavedSearchHistory$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('savedSearchHistory'))
  )

  public getFetchSearchHistoryFailureMessage$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState('fetchSearchHistoryFailureMessage')
    )
  )

  public setSearchResponse(searchResponse: SearchResponseModel): void {
    this.store.dispatch(
      SearchActions.searchSuccess({
        payload: { searchResponse: searchResponse },
      })
    )
  }

  public deleteSearchHistory(
    projectId: number,
    delSearchHistoryRequestModel: DelSearchHistoryRequestModel
  ): void {
    this.store.dispatch(
      SearchActions.deleteSearchHistory({
        projectId,
        delSearchHistoryRequestModel,
      })
    )
  }

  public getDeleteSearchHistorySuccessResponse$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState('delSearchHistorySuccessResponse')
    )
  )

  public getDeleteSearchHistoryFailureResponse$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState('delSearchHistoryFailureResponse')
    )
  )

  public readonly getIsSearchHistoryLoading$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('isSearchHistoryLoading'))
  )

  public resetSearchState(stateKey: SearchStateKeys): void {
    this.store.dispatch(SearchActions.resetSearchState({ stateKey }))
  }

  public setSearchExpression(searchExpression: string): void {
    this.store.dispatch(SearchActions.setSearchExpression({ searchExpression }))
  }

  public setIncludePC(includePC: boolean): void {
    this.store.dispatch(SearchActions.setIncludePC({ includePC }))
  }

  public setSearchDupOption(searchDupOption: SearchDupOption): void {
    this.store.dispatch(SearchActions.setSearchDupOption({ searchDupOption }))
  }

  public getSearchExpression$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('searchExpression'))
  )

  public getIncludePC$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('includePC'))
  )

  public getSearchDupOption$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('searchDupOption'))
  )

  public fetchDSJobStatus(dsStatus: SearchStatusModel): void {
    this.store.dispatch(SearchActions.fetchDSJobStatus({ dsStatus }))
  }

  public getDSJobStatusSuccessResponse$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState('fetchDSJobStatusSuccessResponse')
    )
  )

  public getDSJobStatusFailureResponse$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState('fetchDSJobStatusFailureResponse')
    )
  )

  public fetchDSJobStatusCount(dsStatus: SearchStatusModel): void {
    this.store.dispatch(SearchActions.fetchDSJobStatusCount({ dsStatus }))
  }

  public getDSJobStatusCountSuccessResponse$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState(
        'fetchDSJobStatusCountSuccessResponse'
      )
    )
  )

  public getDSJobStatusCountFailureResponse$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState(
        'fetchDSJobStatusCountFailureResponse'
      )
    )
  )

  public readonly getIsDSJobStatusLoading$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('isDSJobStatusLoading'))
  )

  public setDSJobStatusLoadiing(isDSJobStatusLoading: boolean): void {
    this.store.dispatch(
      SearchActions.setDSJobStatusLoadiing({ isDSJobStatusLoading })
    )
  }

  // Return Observable<unknown> instead of Observable<ResponseModel> to handle error
  public fetchDSJobStatusCountByService$(
    dsStatus: SearchStatusModel
  ): Observable<unknown> {
    this.setDSJobStatusLoadiing(true)
    return this.searchService.fetchDSJobStatusCount$(dsStatus).pipe(
      catchError((error: unknown) => {
        const httpError = error as HttpErrorResponse
        this.store.dispatch(
          SearchActions.fetchDSJobStatusCountFailure({
            fetchDSJobStatusCountFailureResponse: httpError.error,
          })
        )
        // Return Observable<unknown> instead of Observable<ResponseModel>
        return of([])
      })
    )
  }

  // Return Observable<unknown> instead of Observable<ResponseModel> to handle error
  public fetchDSJobStatusByService$(
    dsStatus: SearchStatusModel
  ): Observable<unknown> {
    this.setDSJobStatusLoadiing(true)
    return this.searchService.fetchDSJobStatus$(dsStatus).pipe(
      catchError((error: unknown) => {
        const httpError = error as HttpErrorResponse
        this.store.dispatch(
          SearchActions.fetchDSJobStatusFailure({
            fetchDSJobStatusFailureResponse: httpError.error,
          })
        )
        return of([])
      })
    )
  }

  public fetchSavedSearchWithTags(projectId: number): void {
    this.store.dispatch(SearchActions.fetchSavedSearchWithTags({ projectId }))
  }

  public getSavedSearchWithTagsSuccessResponse$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState(
        'fetchSavedSearchWithTagsSuccessResponse'
      )
    )
  )

  public getSavedSearchWithTagsFailureResponse$ = this.store.pipe(
    select(
      SearchSelectors.getStateOfSearchState(
        'fetchSavedSearchWithTagsFailureResponse'
      )
    )
  )

  public saveSearch(
    projectId: number,
    saveSearchRequest: SaveSearchRequestModel,
    saveSearchForProduction: boolean
  ): void {
    this.store.dispatch(
      SearchActions.saveSearch({
        projectId,
        saveSearchRequest,
        saveSearchForProduction,
      })
    )
  }

  public getSaveSearchSuccessResponse$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('saveSearchSuccessResponse'))
  )

  public getSaveSearchFailureResponse$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('saveSearchFailureResponse'))
  )

  public readonly getIsSearchSaving$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('isSearchSaving'))
  )

  public getIsSaveSearchForProduction$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('isSaveSearchForProduction'))
  )

  public resetIsSaveSearchForProductionFlag(): void {
    this.store.dispatch(SearchActions.resetIsSaveSearchForProductionFlag())
  }

  public getSearchFailureResponse$ = this.store.pipe(
    select(SearchSelectors.getStateOfSearchState('searchFailureResponse'))
  )

  public resetSearchInputControls(): void {
    this.store.dispatch(SearchActions.resetSearchInputControls())
  }

  public updateTagRuleIdPatternList(tagRuleIdPatternList: {
    ruleIds: number[]
    syntax: string[]
  }): void {
    this.store.dispatch(
      SearchActions.updateTagRuleIdPatternList({
        tagRuleIdPatternList,
      })
    )
  }
}
