<kendo-dialog-titlebar (close)="close('')">
  <div>
    {{ dialogTitle }}
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-flex-col">
  <div class="t-flex t-gap-5">
    <div class="t-flex t-flex-1 t-flex-col t-w-full">
      <kendo-tabstrip
        #mainTabStrip
        class="t-w-full t-h-full"
        (tabSelect)="updateDialogTitle($event)">
        <kendo-tabstrip-tab title="Tag" [selected]="true">
          <ng-template kendoTabContent>
            <div class="content">
              <div class="t-flex t-gap-3">
                <div class="t-w-full">
                  <div class="t-flex t-flex-col">
                    <div
                      class="t-flex t-flex-col"
                      *ngIf="!isTagAddUpdate() && isTagAndCodingVisible">
                      <div
                        class="t-p-3 t-flex t-justify-between t-items-center t-pb-0">
                        <div class="t-w-[28%]">
                          <ng-container
                            *ngComponentOutlet="
                              tagSearchComponent | async
                            "></ng-container>
                        </div>

                        <div class="t-flex">
                          <button
                            kendoButton
                            *venioHasUserGroupRights="rights.ADD_NEW_TAG"
                            (click)="setFlagForTagAddUpdate()"
                            class="v-custom-secondary-button t-mt-1 t-mb-1"
                            fillMode="outline"
                            themeColor="secondary">
                            ADD NEW TAG
                          </button>
                        </div>
                      </div>
                      <div class="t-w-full t-flex t-flex-col">
                        <ng-container
                          *ngComponentOutlet="
                            documentTagComponent | async;
                            inputs: { isConflictDialog: isConflictDialog }
                          ">
                        </ng-container>
                      </div>
                    </div>

                    <!-- edit tag & manage group -->
                    <div class="t-flex t-flex-col" *ngIf="isTagAddUpdate()">
                      <div class="t-flex t-mt-4">
                        <button
                          kendoButton
                          (click)="navigateTagForm()"
                          class="!t-p-0 !t-m-0 t-font-bold"
                          [svgIcon]="leftSvg"
                          fillMode="clear">
                          ALL TAGS
                        </button>
                      </div>
                      <ng-container
                        *ngComponentOutlet="
                          lazyTagFormComp | async
                        "></ng-container>
                    </div>
                  </div>
                </div>
              </div>

              <!-- <div class="t-flex t-flex-col">
                <div class="t-flex t-justify-end">
                  <button
                    *ngIf="
                      !isTagAddUpdate()
                    "
                    (click)="toggleTagFieldForm()"
                    kendoButton
                    class="v-custom-secondary-button t-my-6"
                    themeColor="secondary"
                    fillMode="outline">
                    ADD NEW TAG
                  </button>
                </div>
                <div class="t-flex t-w-full" *ngIf="isTagAndCodingVisible">
                  <ng-container
                    *ngComponentOutlet="
                      tagSearchComponent | async
                    "></ng-container>
                </div>
                <div class="t-flex t-w-full" *ngIf="isTagAndCodingVisible">
                  <ng-container
                    *ngComponentOutlet="
                      documentTagComponent | async
                    "></ng-container>
                </div>
              </div> -->
            </div>
          </ng-template>
        </kendo-tabstrip-tab>
        <kendo-tabstrip-tab
          *venioHasUserGroupRights="rights.ALLOW_ENABLE_DISABLE_BULK_CODING"
          title="Coding">
          <ng-template kendoTabContent>
            <div class="content">
              <div class="t-flex t-gap-3">
                <div class="t-flex t-flex-col t-w-full">
                  <div class="t-flex t-mt-1">
                    <button
                      kendoButton
                      *ngIf="isCodingFieldAddUpdate()"
                      (click)="toggleCodingFieldForm()"
                      class="!t-p-0 !t-m-0 t-font-bold"
                      [svgIcon]="leftSvg"
                      fillMode="clear">
                      ALL CODING FIELDS
                    </button>
                  </div>

                  <div *ngIf="!isCodingFieldAddUpdate()">
                    <div class="t-flex">
                      <div class="t-flex t-w-full t-gap-1 t-flex-col">
                        <!-- TODO: this could be added in future UI can be enabled from here -->
                        <!-- <div
                            class="t-flex t-gap-3 t-mt-2 t-mb-2 t-p-3 t-pt-0 t-pb-0">
                            <input
                              type="radio"
                              #Append
                              value="Append"
                              kendoRadioButton
                              name="appendOrReplace"
                              checked />
                            <kendo-label [for]="Append" text="Append"></kendo-label>

                            <input
                              type="radio"
                              #replace
                              value="replace"
                              kendoRadioButton
                              name="appendOrReplace" />
                            <kendo-label
                              [for]="replace"
                              text="Replace with new value"></kendo-label>
                          </div> -->
                      </div>
                    </div>
                    <ng-container
                      *ngComponentOutlet="
                        multiValueCodingFieldContainer | async
                      "></ng-container>

                    <div
                      class="t-flex t-justify-between t-flex-row-reverse t-items-center t-p-3">
                      <div class="t-flex t-flex-0">
                        <button
                          kendoButton
                          *ngIf="
                            !isCodingFieldAddUpdate() &&
                            isDocumentCodingComponentLoaded()
                          "
                          (click)="toggleCodingFieldForm()"
                          class="v-custom-secondary-button"
                          fillMode="outline"
                          themeColor="secondary">
                          Add new coding field
                        </button>
                      </div>
                      <div class="t-inline t-text-left t-place-self-start">
                        <p class="t-text-[#232323] t-font-semibold t-text-xs">
                          <span class="t-text-error mr-2">NOTE</span> Changes
                          will only apply to the fields where the checkboxes are
                          checked.
                        </p>
                      </div>
                    </div>

                    <div
                      class="t-flex v-parent-container t-flex-full v-hide-scrollbar"
                      *ngIf="isTagAndCodingVisible">
                      <div #codingContainer class="t-flex t-flex-col t-w-2/5">
                        <div class="t-flex t-flex-col t-ml-[12.3%] t-mr-[9.7%]">
                          @defer{
                          <venio-coding-search />
                          }
                        </div>

                        @defer{
                        <venio-document-coding
                          class="t-overflow-y-auto"
                          [ngStyle]="{
                            'height.px': codingContainer.offsetHeight - 70
                          }" />
                        }
                      </div>

                      <!-- grid starts here -->
                      <div class="t-flex t-flex-col t-w-3/5 t-ml-[1%]">
                        <ng-container
                          *ngComponentOutlet="
                            bulkCodingList | async
                          "></ng-container>
                      </div>
                      <!-- grid end -->
                    </div>
                  </div>

                  <div *ngIf="isCodingFieldAddUpdate()">
                    <ng-container
                      *ngComponentOutlet="
                        codingFieldFormComp | async;
                        inputs: { isCodingFormValid }
                      " />
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </kendo-tabstrip-tab>
      </kendo-tabstrip>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="col-md-9">
    <span
      *ngIf="isTagTab() && isTagRuleListLoaded$ | async"
      kendoPopoverAnchor
      [popover]="tagRuleDescriptionPopover"
      showOn="click"
      class="t-cursor-pointer t-text-[var(--v-custom-sky-blue)] t-font-medium t-uppercase"
      (click)="showAllTagRules()">
      All tag rules</span
    >
    <span
      class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-2"
      *ngIf="formMessage"
      >{{ formMessage }}</span
    >
  </div>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      [disabled]="
        !isTagAndCodingVisible ||
        isSavingData() ||
        (isTagAddUpdate() && !isTagFormValid()) ||
        (isCodingFieldAddUpdate() && !isValidCodingForm) ||
        (isCodingTab() && !isCodingDataValid())
      "
      (click)="saveTag()"
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary">
      <kendo-loader
        *ngIf="isSavingData()"
        themeColor="secondary"
        type="infinite-spinner"
        size="small"></kendo-loader>
      SAVE
    </button>
    <button
      kendoButton
      (click)="close('yes')"
      themeColor="dark"
      fillMode="outline">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>

<kendo-popover
  #tagRuleDescriptionPopover
  position="right"
  width="300"
  [animation]="{ type: 'slide', direction: 'right', duration: 100 }">
  <ng-template kendoPopoverBodyTemplate>
    <ng-container *ngComponentOutlet="tagRuleInfoComp | async"></ng-container>
  </ng-template>
</kendo-popover>
