<div class="t-flex">
  <div class="t-flex t-flex-col t-p-3 t-w-full">
    <div class="t-flex t-w-full t-justify-end">
      <button
        kendoButton
        id="toogleSearchBtn"
        class="t-p-1 t-leading-none t-text-[#979797] t-border-[#979797]"
        themeColor="inverse"
        fillMode="outline"
        title="Options"
        size="none"
        (click)="toggleSearchFilterDisplay()">
        <kendo-svg-icon [icon]="gearIcon"></kendo-svg-icon>
      </button>
    </div>

    <div class="t-flex t-flex-col t-gap-3 t-transition-all" [@slide]="state">
      <form [formGroup]="searchScopeForm">
        <h3 class="t-text-primary t-font-semibold">Similar Search Scope</h3>
        <div class="t-flex t-mt-3 t-gap-3 t-text-sm">
          <div class="t-flex t-flex-row-reverse t-gap-2">
            <label
              [class]="
                selectedSearchScope === searchScope.ALL_DOCUMENTS
                  ? 't-text-primary t-font-semibold k-radio-label'
                  : 'k-radio-label'
              "
              for="allDoc">
              {{ searchScopeTitle.ALL_DOCUMENTS }}
            </label>
            <input
              type="radio"
              name="searchScopeType"
              id="allDoc"
              formControlName="searchScopeType"
              [value]="searchScope.ALL_DOCUMENTS"
              kendoRadioButton />
          </div>

          <div class="t-flex t-flex-row-reverse t-gap-2">
            <label
              [class]="
                selectedSearchScope === searchScope.ALL_DOCUMENTS
                  ? 't-text-primary t-font-semibold k-radio-label'
                  : 'k-radio-label'
              "
              for="selDoc">
              {{ searchScopeTitle.SELECTED_MEDIA }}
            </label>
            <input
              type="radio"
              name="searchScopeType"
              id="selMedia"
              formControlName="searchScopeType"
              [value]="searchScope.SELECTED_MEDIA"
              kendoRadioButton />
          </div>
        </div>

        <div class="t-flex t-w-full t-mt-1">
          <div class="t-flex t-gap-2 t-mt-3 t-items-center t-w-full">
            <!-- kendo slider -->
            <div class="t-flex t-flex-col t-gap-2 t-w-[80%] t-max-w-[400px]">
              <div class="t-flex t-text-sm">
                Minimum similarity score
                <span class="t-font-semibold t-pl-1"
                  >{{ currentSimilarityScore() }}%</span
                >
              </div>
              <kendo-slider
                formControlName="slider"
                [min]="0"
                [max]="100"
                [smallStep]="1"
                [tickPlacement]="'none'"
                class="v-custom-slider t-w-full"
                [showButtons]="false">
                <kendo-slider-messages
                  increment="Right"
                  decrement="Left"></kendo-slider-messages>
              </kendo-slider>
            </div>

            <button
              kendoButton
              class="t-p-1 t-leading-none t-px-2 t-self-end t-ml-3 t-h-[36px]"
              fillMode="outline"
              title="Search"
              size="none"
              themeColor="secondary"
              (click)="search()">
              <kendo-svg-icon [icon]="searchIcon"></kendo-svg-icon>
            </button>
          </div>
        </div>
      </form>
    </div>

    <div class="t-flex t-flex-col t-mt-4">
      <kendo-grid
        #grid
        [kendoGridBinding]="similarDocuments"
        [loading]="isSimilarDocumentLoading$ | async"
        [rowClass]="rowClass"
        [resizable]="true"
        [sortable]="true"
        [autoSize]="true"
        [pageable]="{ type: 'numeric', position: 'top' }"
        [pageSize]="pageSize"
        [trackBy]="similarDocumentTrackByFn"
        (dataStateChange)="onDataStateChange()">
        <ng-template kendoGridNoRecordsTemplate>
          <div class="t-flex t-h-min t-w-full">
            <span class="t-text-[#000000BC] t-text-[16px]"
              >No records found</span
            >
          </div>
        </ng-template>
        <ng-template kendoPagerTemplate>
          <div>Total No of records : {{ totalHitCount() }}</div>
          <kendo-grid-spacer></kendo-grid-spacer>
          <venio-pagination
            *ngIf="totalHitCount() > pageSize"
            [totalRecords]="totalHitCount()"
            [pageSize]="pageSize"
            [showPageJumper]="false"
            [showPageSize]="false"
            [showRowNumberInputBox]="false"
            (pageChanged)="pageChanged($event)"
            class="t-px-5 t-block t-py-2">
          </venio-pagination>
        </ng-template>
        <kendo-grid-column
          title="Details"
          class="t-text-center"
          [headerClass]="[
            't-text-primary',
            totalHitCount() === 0 ? 't-min-w-[150px]' : ''
          ]"
          [width]="250"
          [minResizableWidth]="100">
          <ng-template kendoGridHeaderTemplate let-column>
            <span kendoTooltip title="Details">Details</span>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <span
              class="t-cursor-pointer"
              (click)="onDetailsClicked(dataItem)"
              venioSvgLoader
              hoverColor="#FFBB12"
              color="#979797"
              [svgUrl]="'assets/svg/icon-show-fields.svg'"
              height="1rem"
              width="1rem">
              <kendo-loader size="small" />
            </span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column
          *ngFor="let field of headers"
          [field]="field"
          headerClass="t-text-primary">
          <ng-template kendoGridHeaderTemplate let-column>
            <span kendoTooltip [title]="field">{{ field }}</span>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <span kendoTooltip [title]="dataItem[field]">{{
              dataItem[field]
            }}</span>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </div>
</div>
