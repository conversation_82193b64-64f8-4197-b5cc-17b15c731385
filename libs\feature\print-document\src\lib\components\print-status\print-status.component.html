<div class="t-flex t-flex-col t-h-full">
  <venio-pagination
    [disabled]="statusData()?.length === 0"
    [totalRecords]="statusData()?.length"
    [pageSize]="pageSize()"
    [currentPage]="currentPage()"
    [showPageJumper]="false"
    [showPageSize]="true"
    [showRowNumberInputBox]="true"
    class="t-block t-py-2"
    (pageChanged)="onPageChange($event)"
    (pageSizeChanged)="onPageSizeChange($event)">
  </venio-pagination>

  <div class="t-h-full" #gridContainer>
    <div class="t-flex t-flex-row t-place-self-center t-h-full">
      <kendo-grid
        id="status-grid"
        class="v-custom-grid-table"
        venioDynamicHeight
        [isKendoDialog]="true"
        *ngIf="!isStatusLoading()"
        [kendoGridBinding]="paginatedData()"
        [rowReorderable]="true"
        [pageSize]="pageSize()"
        [resizable]="true">
        <kendo-grid-column
          field="PrintJobName"
          title="Name"
          [headerClass]="[
            '!t-text-primary',
            '!t-bg-[#F7F7F7]',
            '!t-border-t-[1px] !t-border-t-[#DADADA]'
          ]">
        </kendo-grid-column>
        <kendo-grid-column
          field="CreatedDate"
          title="Queued On"
          [width]="250"
          [headerClass]="[
            '!t-text-primary',
            '!t-bg-[#F7F7F7]',
            '!t-border-t-[1px] !t-border-t-[#DADADA]'
          ]">
        </kendo-grid-column>
        <kendo-grid-column
          field="JobStatus"
          title="Status"
          [width]="220"
          [headerClass]="[
            '!t-text-primary',
            '!t-bg-[#F7F7F7]',
            '!t-border-t-[1px] !t-border-t-[#DADADA]'
          ]">
        </kendo-grid-column>
        <kendo-grid-column
          title="Action"
          [width]="200"
          [reorderable]="false"
          [headerClass]="[
            '!t-text-primary',
            '!t-bg-[#F7F7F7]',
            '!t-border-t-[1px] !t-border-t-[#DADADA]'
          ]">
          <ng-template
            kendoGridCellTemplate
            let-dataItem
            let-rowIndex="rowIndex">
            <kendo-buttongroup>
              <button
                kendoButton
                #actionGridDownload
                class="!t-py-[0.38rem] !t-px-[0.5rem] t-bg-white t-border t-border-[#263238] t-w-[32px] t-h-[25px] hover:t-border-[#1EBADC] hover:t-bg-[#1EBADC]"
                (click)="onDownloadClicked(dataItem)"
                [disabled]="dataItem.JobStatus !== 'Completed'"
                kendoTooltip
                [title]="'DOWNLOAD'"
                size="none">
                <span
                  [parentElement]="actionGridDownload.element"
                  venioSvgLoader
                  [svgUrl]="downloadSvgUrl"
                  hoverColor="#FFF"
                  height="1rem"
                  width="1.1rem"></span>
              </button>
              <button
                kendoButton
                #actionGridDelete
                class="!t-py-[0.38rem] !t-px-[0.5rem] t-bg-white t-border t-border-[#263238] t-w-[32px] t-h-[25px] hover:t-border-[#ED7425] hover:t-bg-[#ED7425]"
                (click)="onDeleteClicked(dataItem)"
                [disabled]="dataItem.JobStatus !== 'Completed'"
                kendoTooltip
                [title]="'DELETE'"
                size="none">
                <span
                  [parentElement]="actionGridDelete.element"
                  venioSvgLoader
                  [svgUrl]="deleteSvgUrl"
                  hoverColor="#FFF"
                  height="0.75rem"
                  width="0.6rem"></span>
              </button>
            </kendo-buttongroup>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
      <kendo-loader
        *ngIf="isStatusLoading()"
        size="medium"
        type="pulsing"></kendo-loader>
    </div>
  </div>
</div>

<ng-template #printStatusActionTemplate>
  <div class="t-flex t-flex-row t-justify-end">
    <!--kept empty for now-->
  </div>
</ng-template>
