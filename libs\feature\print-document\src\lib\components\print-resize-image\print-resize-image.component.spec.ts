import {
  ComponentFix<PERSON>,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { PrintResizeImageComponent } from './print-resize-image.component'
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'

import { CommonModule } from '@angular/common'
import { InputsModule, RadioButtonModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { LOCALE_ID } from '@angular/core'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { TiffPaperSizes } from '../../models/print-summary.models'

describe('PrintResizeImageComponent', () => {
  let component: PrintResizeImageComponent
  let fixture: ComponentFixture<PrintResizeImageComponent>
  let mockPrintDocumentFormService: Partial<PrintDocumentFormService>
  let mockFormGroup: FormGroup

  beforeEach(async () => {
    // Create a mock form group that mimics the structure of imageResizeOption
    const formBuilder = new FormBuilder()
    mockFormGroup = formBuilder.group({
      resizeImage: [false],
      paperSize: [{ value: 0, disabled: true }],
      dimension: [{ value: 'BOTH', disabled: true }],
      width: [{ value: 8.5, disabled: true }],
      height: [{ value: 11, disabled: true }],
      sizeUnit: [{ value: 'INCH', disabled: true }],
      maintainAspectRatio: [{ value: false, disabled: true }],
    })

    // Create a mock PrintDocumentFormService
    mockPrintDocumentFormService = {
      printForm: formBuilder.group({
        imageResizeOption: mockFormGroup,
      }),
    }

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        ReactiveFormsModule,
        InputsModule,
        LabelModule,
        DropDownListModule,
        RadioButtonModule,
        NoopAnimationsModule,
        PrintResizeImageComponent,
      ],
      providers: [
        {
          provide: PrintDocumentFormService,
          useValue: mockPrintDocumentFormService,
        },
        FormBuilder,
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: LOCALE_ID, useValue: 'en-US' },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PrintResizeImageComponent)
    component = fixture.componentInstance

    // Initialize the form service
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should have a valid imageResizeOption FormGroup', () => {
    expect(component.imageResizeOption).toBeDefined()
    expect(component.imageResizeOption instanceof FormGroup).toBeTruthy()
  })

  it('should have tiffPaperSizesList populated with correct values', () => {
    expect(component.tiffPaperSizesList).toBeDefined()
    expect(component.tiffPaperSizesList).toHaveLength(7) // 7 paper sizes including CUSTOM_SIZE

    // Check that the list contains the expected paper sizes
    const paperSizes = component.tiffPaperSizesList.map((item) => item.value)
    expect(paperSizes).toContain(TiffPaperSizes.LETTER)
    expect(paperSizes).toContain(TiffPaperSizes.A3)
    expect(paperSizes).toContain(TiffPaperSizes.A4)
    expect(paperSizes).toContain(TiffPaperSizes.LEGAL)
    expect(paperSizes).toContain(TiffPaperSizes.LEDGER)
    expect(paperSizes).toContain(TiffPaperSizes.TABLOID)
    expect(paperSizes).toContain(TiffPaperSizes.CUSTOM_SIZE)
  })

  it('should enable all controls when resizeImage is checked', fakeAsync(() => {
    // GIVEN the resizeImage checkbox is initially unchecked
    expect(component.imageResizeOption.get('resizeImage')?.value).toBe(false)

    // WHEN the resizeImage checkbox is checked
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription

    // THEN all other controls should be enabled (except width/height for non-custom sizes)
    expect(component.imageResizeOption.get('paperSize')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('sizeUnit')?.enabled).toBe(true)

    // Width and height should still be disabled for predefined paper sizes
    expect(component.imageResizeOption.get('width')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)
  }))

  it('should disable all controls when resizeImage is unchecked', fakeAsync(() => {
    // GIVEN the resizeImage checkbox is checked
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription

    // WHEN the resizeImage checkbox is unchecked
    component.imageResizeOption.get('resizeImage')?.setValue(false)
    tick() // Process the subscription

    // THEN all other controls should be disabled
    expect(component.imageResizeOption.get('paperSize')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('dimension')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('width')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('sizeUnit')?.enabled).toBe(false)
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(false)
  }))

  it('should handle paper size change to CUSTOM_SIZE correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription

    // WHEN paper size is changed to CUSTOM_SIZE
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // THEN width and height controls should be enabled
    expect(component.imageResizeOption.get('width')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(true)

    // AND dimension control should be enabled
    expect(component.imageResizeOption.get('dimension')?.enabled).toBe(true)
  }))

  it('should update dimensions when paper size changes to a predefined size', fakeAsync(() => {
    // GIVEN resizeImage is checked
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription

    // AND sizeUnit is set to INCH
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // WHEN paper size is changed to LETTER
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription

    // THEN width and height should be updated with the correct dimensions
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5)
    expect(component.imageResizeOption.get('height')?.value).toBe(11)
  }))

  it('should handle dimension change to WIDTH correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to WIDTH
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // THEN width control should be enabled and height control should be disabled
    expect(component.imageResizeOption.get('width')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)
  }))

  it('should handle dimension change to HEIGHT correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to HEIGHT
    component.imageResizeOption.get('dimension')?.setValue('HEIGHT')
    tick() // Process the subscription

    // THEN height control should be enabled and width control should be disabled
    expect(component.imageResizeOption.get('width')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(true)
  }))

  it('should handle dimension change to BOTH correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to BOTH
    component.imageResizeOption.get('dimension')?.setValue('BOTH')
    tick() // Process the subscription

    // THEN both width and height controls should be enabled
    expect(component.imageResizeOption.get('width')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(true)

    // AND maintainAspectRatio should be disabled and set to false
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(false)
    expect(component.imageResizeOption.get('maintainAspectRatio')?.value).toBe(
      false
    )
  }))

  it('should update validators when dimension changes for CUSTOM_SIZE', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to WIDTH
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // THEN width should have required validator
    expect(component.imageResizeOption.get('width')?.hasValidator).toBeTruthy()

    // AND width should be invalid when empty
    component.imageResizeOption.get('width')?.setValue('')
    expect(component.imageResizeOption.get('width')?.valid).toBe(false)
    expect(
      component.imageResizeOption.get('width')?.errors?.['required']
    ).toBeTruthy()

    // Check if height has required validator (it might have other validators)
    const heightControl = component.imageResizeOption.get('height')
    const hasRequiredValidator = heightControl?.hasValidator(
      Validators.required
    )
    expect(hasRequiredValidator).toBeFalsy()
  }))

  it('should update dimensions when size unit changes', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is LETTER
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription

    // AND sizeUnit is INCH
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // Initial values for LETTER in inches
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5)
    expect(component.imageResizeOption.get('height')?.value).toBe(11)

    // WHEN sizeUnit is changed to PIXEL
    component.imageResizeOption.get('sizeUnit')?.setValue('PIXEL')
    tick() // Process the subscription

    // THEN dimensions should be updated (multiplied by 300 DPI)
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5 * 300)
    expect(component.imageResizeOption.get('height')?.value).toBe(11 * 300)
  }))

  it('should clean up subscriptions on destroy', () => {
    // GIVEN the component is initialized

    // WHEN ngOnDestroy is called
    const nextSpy = jest.spyOn(component['toDestory$'], 'next')
    const completeSpy = jest.spyOn(component['toDestory$'], 'complete')

    component.ngOnDestroy()

    // THEN the subject should be completed
    expect(nextSpy).toHaveBeenCalled()
    expect(completeSpy).toHaveBeenCalled()
  })

  it('should update validators for HEIGHT dimension', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to HEIGHT
    component.imageResizeOption.get('dimension')?.setValue('HEIGHT')
    tick() // Process the subscription

    // THEN height should have required validator
    const heightControl = component.imageResizeOption.get('height')
    const hasRequiredValidator = heightControl?.hasValidator(
      Validators.required
    )
    expect(hasRequiredValidator).toBeTruthy()

    // AND height should be invalid when empty
    component.imageResizeOption.get('height')?.setValue('')
    expect(component.imageResizeOption.get('height')?.valid).toBe(false)
    expect(
      component.imageResizeOption.get('height')?.errors?.['required']
    ).toBeTruthy()

    // AND width should not have required validator
    const widthControl = component.imageResizeOption.get('width')
    const widthHasRequiredValidator = widthControl?.hasValidator(
      Validators.required
    )
    expect(widthHasRequiredValidator).toBeFalsy()
  }))

  it('should update validators for BOTH dimension', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to BOTH
    component.imageResizeOption.get('dimension')?.setValue('BOTH')
    tick() // Process the subscription

    // THEN both width and height should have required validator
    const widthControl = component.imageResizeOption.get('width')
    const widthHasRequiredValidator = widthControl?.hasValidator(
      Validators.required
    )
    expect(widthHasRequiredValidator).toBeTruthy()

    const heightControl = component.imageResizeOption.get('height')
    const heightHasRequiredValidator = heightControl?.hasValidator(
      Validators.required
    )
    expect(heightHasRequiredValidator).toBeTruthy()

    // AND both should be invalid when empty
    component.imageResizeOption.get('width')?.setValue('')
    component.imageResizeOption.get('height')?.setValue('')
    expect(component.imageResizeOption.get('width')?.valid).toBe(false)
    expect(component.imageResizeOption.get('height')?.valid).toBe(false)
  }))

  it('should clear validators when paper size changes from CUSTOM_SIZE to predefined size', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE with validators
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription
    component.imageResizeOption.get('dimension')?.setValue('BOTH')
    tick() // Process the subscription

    // Verify validators are set
    const widthControl = component.imageResizeOption.get('width')
    const widthHasRequiredValidator = widthControl?.hasValidator(
      Validators.required
    )
    expect(widthHasRequiredValidator).toBeTruthy()

    // WHEN paper size is changed to a predefined size
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription

    // THEN validators should be cleared
    const widthControlAfter = component.imageResizeOption.get('width')
    const widthHasRequiredValidatorAfter = widthControlAfter?.hasValidator(
      Validators.required
    )
    expect(widthHasRequiredValidatorAfter).toBeFalsy()
  }))

  it('should handle maintainAspectRatio changes correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked and dimension is WIDTH
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // WHEN maintainAspectRatio is toggled
    component.imageResizeOption.get('maintainAspectRatio')?.setValue(true)
    tick() // Process the subscription

    // THEN height should be disabled
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)
  }))

  it('should apply min validator along with required validator for WIDTH dimension', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to WIDTH
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // THEN width should be invalid when value is 0 (min validator)
    component.imageResizeOption.get('width')?.setValue(0)
    expect(component.imageResizeOption.get('width')?.valid).toBe(false)
    expect(
      component.imageResizeOption.get('width')?.errors?.['min']
    ).toBeTruthy()

    // AND width should be valid when value is greater than 0
    component.imageResizeOption.get('width')?.setValue(1)
    expect(component.imageResizeOption.get('width')?.valid).toBe(true)
  }))

  it('should apply min validator along with required validator for HEIGHT dimension', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to HEIGHT
    component.imageResizeOption.get('dimension')?.setValue('HEIGHT')
    tick() // Process the subscription

    // THEN height should be invalid when value is 0 (min validator)
    component.imageResizeOption.get('height')?.setValue(0)
    expect(component.imageResizeOption.get('height')?.valid).toBe(false)
    expect(
      component.imageResizeOption.get('height')?.errors?.['min']
    ).toBeTruthy()

    // AND height should be valid when value is greater than 0
    component.imageResizeOption.get('height')?.setValue(1)
    expect(component.imageResizeOption.get('height')?.valid).toBe(true)
  }))

  it('should apply min validator for both WIDTH and HEIGHT when dimension is BOTH', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to BOTH
    component.imageResizeOption.get('dimension')?.setValue('BOTH')
    tick() // Process the subscription

    // THEN both width and height should be invalid when values are 0
    component.imageResizeOption.get('width')?.setValue(0)
    component.imageResizeOption.get('height')?.setValue(0)
    expect(component.imageResizeOption.get('width')?.valid).toBe(false)
    expect(component.imageResizeOption.get('height')?.valid).toBe(false)
    expect(
      component.imageResizeOption.get('width')?.errors?.['min']
    ).toBeTruthy()
    expect(
      component.imageResizeOption.get('height')?.errors?.['min']
    ).toBeTruthy()

    // AND both should be valid when values are greater than 0
    component.imageResizeOption.get('width')?.setValue(1)
    component.imageResizeOption.get('height')?.setValue(1)
    expect(component.imageResizeOption.get('width')?.valid).toBe(true)
    expect(component.imageResizeOption.get('height')?.valid).toBe(true)
  }))

  it('should update dimensions correctly for A3 paper size in inches', fakeAsync(() => {
    // GIVEN resizeImage is checked and sizeUnit is INCH
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // WHEN paper size is changed to A3
    component.imageResizeOption.get('paperSize')?.setValue(TiffPaperSizes.A3)
    tick() // Process the subscription

    // THEN width and height should be updated with A3 dimensions in inches
    expect(component.imageResizeOption.get('width')?.value).toBe(11.69)
    expect(component.imageResizeOption.get('height')?.value).toBe(16.54)
  }))

  it('should update dimensions correctly for A4 paper size in pixels', fakeAsync(() => {
    // GIVEN resizeImage is checked and sizeUnit is PIXEL
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption.get('sizeUnit')?.setValue('PIXEL')
    tick() // Process the subscription

    // WHEN paper size is changed to A4
    component.imageResizeOption.get('paperSize')?.setValue(TiffPaperSizes.A4)
    tick() // Process the subscription

    // THEN width and height should be updated with A4 dimensions in pixels (multiplied by 300 DPI)
    expect(component.imageResizeOption.get('width')?.value).toBe(8.27 * 300)
    expect(component.imageResizeOption.get('height')?.value).toBe(11.69 * 300)
  }))

  it('should update dimensions correctly for LEGAL paper size', fakeAsync(() => {
    // GIVEN resizeImage is checked and sizeUnit is INCH
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // WHEN paper size is changed to LEGAL
    component.imageResizeOption.get('paperSize')?.setValue(TiffPaperSizes.LEGAL)
    tick() // Process the subscription

    // THEN width and height should be updated with LEGAL dimensions
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5)
    expect(component.imageResizeOption.get('height')?.value).toBe(14)
  }))

  it('should update dimensions correctly for LEDGER paper size', fakeAsync(() => {
    // GIVEN resizeImage is checked and sizeUnit is INCH
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // WHEN paper size is changed to LEDGER
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LEDGER)
    tick() // Process the subscription

    // THEN width and height should be updated with LEDGER dimensions
    expect(component.imageResizeOption.get('width')?.value).toBe(17)
    expect(component.imageResizeOption.get('height')?.value).toBe(11)
  }))

  it('should update dimensions correctly for TABLOID paper size', fakeAsync(() => {
    // GIVEN resizeImage is checked and sizeUnit is INCH
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // WHEN paper size is changed to TABLOID
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.TABLOID)
    tick() // Process the subscription

    // THEN width and height should be updated with TABLOID dimensions
    expect(component.imageResizeOption.get('width')?.value).toBe(11)
    expect(component.imageResizeOption.get('height')?.value).toBe(17)
  }))

  it('should handle unit conversion with WIDTH dimension correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked, paper size is LETTER, and dimension is WIDTH
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // Initial values should be set correctly for WIDTH dimension
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5)
    expect(component.imageResizeOption.get('height')?.value).toBe(0) // Should be 0 for WIDTH dimension

    // WHEN sizeUnit is changed to PIXEL
    component.imageResizeOption.get('sizeUnit')?.setValue('PIXEL')
    tick() // Process the subscription

    // THEN width should be converted to pixels and height should remain 0
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5 * 300)
    expect(component.imageResizeOption.get('height')?.value).toBe(0)
  }))

  it('should handle unit conversion with HEIGHT dimension correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked, paper size is LETTER, and dimension is HEIGHT
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription
    component.imageResizeOption.get('dimension')?.setValue('HEIGHT')
    tick() // Process the subscription
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // Initial values should be set correctly for HEIGHT dimension
    expect(component.imageResizeOption.get('width')?.value).toBe(0) // Should be 0 for HEIGHT dimension
    expect(component.imageResizeOption.get('height')?.value).toBe(11)

    // WHEN sizeUnit is changed to PIXEL
    component.imageResizeOption.get('sizeUnit')?.setValue('PIXEL')
    tick() // Process the subscription

    // THEN height should be converted to pixels and width should remain 0
    expect(component.imageResizeOption.get('width')?.value).toBe(0)
    expect(component.imageResizeOption.get('height')?.value).toBe(11 * 300)
  }))

  it('should not perform unit conversion for CUSTOM_SIZE paper', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // Set custom values
    component.imageResizeOption.get('width')?.setValue(10)
    component.imageResizeOption.get('height')?.setValue(15)
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // WHEN sizeUnit is changed to PIXEL
    component.imageResizeOption.get('sizeUnit')?.setValue('PIXEL')
    tick() // Process the subscription

    // THEN values should remain unchanged (no unit conversion for custom size)
    expect(component.imageResizeOption.get('width')?.value).toBe(10)
    expect(component.imageResizeOption.get('height')?.value).toBe(15)
  }))

  it('should disable dimension control and set to BOTH when paper size is not CUSTOM_SIZE', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE with dimension as WIDTH
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // Verify dimension control is enabled for CUSTOM_SIZE
    expect(component.imageResizeOption.get('dimension')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('dimension')?.value).toBe('WIDTH')

    // WHEN paper size is changed to a predefined size (LETTER)
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription

    // THEN dimension should be set to BOTH and controls should be disabled
    expect(component.imageResizeOption.get('dimension')?.value).toBe('BOTH')
    expect(component.imageResizeOption.get('width')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(false)
  }))

  it('should handle maintainAspectRatio changes for HEIGHT dimension', fakeAsync(() => {
    // GIVEN resizeImage is checked, paper size is CUSTOM_SIZE, and dimension is HEIGHT
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription
    component.imageResizeOption.get('dimension')?.setValue('HEIGHT')
    tick() // Process the subscription

    // WHEN maintainAspectRatio is toggled
    component.imageResizeOption.get('maintainAspectRatio')?.setValue(true)
    tick() // Process the subscription

    // THEN width should be disabled for HEIGHT dimension
    expect(component.imageResizeOption.get('width')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(true)
  }))

  it('should clear width and height values when changing to CUSTOM_SIZE', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is LETTER with values set
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription

    // Verify values are set for LETTER
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5)
    expect(component.imageResizeOption.get('height')?.value).toBe(11)

    // WHEN paper size is changed to CUSTOM_SIZE
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // THEN width and height values should be cleared (empty string)
    expect(component.imageResizeOption.get('width')?.value).toBe('')
    expect(component.imageResizeOption.get('height')?.value).toBe('')
    expect(component.imageResizeOption.get('width')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(true)
  }))

  it('should set maintainAspectRatio to true and disable it for WIDTH and HEIGHT dimensions', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to WIDTH
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // THEN maintainAspectRatio should be disabled and set to true
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(false)
    expect(component.imageResizeOption.get('maintainAspectRatio')?.value).toBe(
      true
    )

    // WHEN dimension is changed to HEIGHT
    component.imageResizeOption.get('dimension')?.setValue('HEIGHT')
    tick() // Process the subscription

    // THEN maintainAspectRatio should still be disabled and set to true
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(false)
    expect(component.imageResizeOption.get('maintainAspectRatio')?.value).toBe(
      true
    )
  }))
})
