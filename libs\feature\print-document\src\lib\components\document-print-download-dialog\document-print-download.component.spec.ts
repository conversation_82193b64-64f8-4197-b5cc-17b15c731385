import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentPrintDownloadComponent } from './document-print-download.component'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { PrintImageActionTemplateService } from '@venio/data-access/review'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TabStripModule } from '@progress/kendo-angular-layout'
import { ProgressBarModule } from '@progress/kendo-angular-progressbar'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { provideHttpClient } from '@angular/common/http'

describe('DocumentPrintDownloadComponent', () => {
  let component: DocumentPrintDownloadComponent
  let fixture: ComponentFixture<DocumentPrintDownloadComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DocumentPrintDownloadComponent],
      imports: [
        NoopAnimationsModule,
        DialogModule,
        GridModule,
        InputsModule,
        LabelModule,
        ProgressBarModule,
        TabStripModule,
        SvgLoaderDirective,
      ],
      providers: [
        provideHttpClient(),
        DialogRef,
        PrintImageActionTemplateService,
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentPrintDownloadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
