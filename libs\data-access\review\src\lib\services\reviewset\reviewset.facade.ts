import { Injectable } from '@angular/core'
import { ReviewSetService } from './reviewset.service'
import { Observable, Subject } from 'rxjs'
import { MarkAsReviewedRequestModel } from '../../models/interfaces'

@Injectable({
  providedIn: 'root',
})
export class ReviewsetFacade {
  public checkoutBatchReviewSetAction: Subject<number> = new Subject<number>()
  public checkBatchReviewCompletedAction$: Subject<number> =
    new Subject<number>()
  public markCurrentDocumentAsReviewedAction: Subject<number> =
    new Subject<number>()

  constructor(private reviewsetService: ReviewSetService) {}

  public fetchReviewSetBasicInfo$<T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> {
    return this.reviewsetService.fetchReviewSetBasicInfo$<T>(
      projectId,
      reviewSetId
    )
  }
  public markAsReviewed$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number,
    fileId: number,
    requestModel: MarkAsReviewedRequestModel
  ): Observable<T> {
    return this.reviewsetService.markAsReviewed$<T>(
      projectId,
      reviewSetId,
      batchId,
      fileId,
      requestModel
    )
  }

  public markAsReviewedBulk$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number,
    requestModel: MarkAsReviewedRequestModel
  ): Observable<T> {
    return this.reviewsetService.markAsReviewedBulk$<T>(
      projectId,
      reviewSetId,
      batchId,
      requestModel
    )
  }

  public fetchReviewSetBatchInfo$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number
  ): Observable<T> {
    return this.reviewsetService.fetchReviewSetBatchInfo$<T>(
      projectId,
      reviewSetId,
      batchId
    )
  }

  public isBatchReviewCompleted$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number
  ): Observable<T> {
    return this.reviewsetService.isBatchReviewCompleted$<T>(
      projectId,
      reviewSetId,
      batchId
    )
  }

  public checkInReviewBatch$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number
  ): Observable<T> {
    return this.reviewsetService.checkInReviewBatch$<T>(
      projectId,
      reviewSetId,
      batchId
    )
  }
}
