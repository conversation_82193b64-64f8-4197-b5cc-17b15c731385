<kendo-dialog-titlebar>
  <div class="t-flex t-items-center t-justify-center">
    <div
      class="t-flex t-items-center t-justify-center t-w-10 t-h-10 t-bg-[#0000000C] t-bg-no-repeat t-rounded-full t-bg-[0%_0%]">
      <span
        venioSvgLoader
        [svgUrl]="findAndRepaceSvgUrl"
        height="1rem"
        width="1.1rem"></span>
    </div>
    <span
      class="t-ml-2 t-font-medium t-text-[1rem] t-text-[#2F3080] t-opacity-100">
      Find & Replace
    </span>
  </div>
</kendo-dialog-titlebar>

<div class="container-size">
  <kendo-tabstrip
    #mainTabStrip
    class="t-min-h-[30rem] t-min-w-[35rem] t-h-full"
    (tabSelect)="onTabSelect($event)">
    <kendo-tabstrip-tab title="Find & Replace" [selected]="true">
      <ng-template kendoTabContent>
        <div class="t-py-2 t-h-full">
          <ng-container
            *ngComponentOutlet="fieldValueComponent | async"></ng-container>
        </div>
      </ng-template>
    </kendo-tabstrip-tab>
    <kendo-tabstrip-tab title="Merge Field Value">
      <ng-template kendoTabContent>
        <div class="t-py-2 t-h-full">
          <ng-container
            #MergedFieldValueComponent
            *ngComponentOutlet="
              mergedFieldComponent | async;
              inputs: mergeUi
            "></ng-container>
        </div>
      </ng-template>
    </kendo-tabstrip-tab>
    <kendo-tabstrip-tab title="Field Value in Priority Order">
      <ng-template kendoTabContent>
        <div class="t-py-2 t-h-full">
          <ng-container
            *ngComponentOutlet="
              priorityFieldComponent | async;
              inputs: priorityUi
            "></ng-container>
        </div>
      </ng-template>
    </kendo-tabstrip-tab>
  </kendo-tabstrip>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      id="apply"
      [disabled]="isReplacing | async"
      (click)="replace()"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="apply">
      <kendo-loader
        *ngIf="isReplacing | async"
        themeColor="secondary"
        type="infinite-spinner"
        size="small"></kendo-loader>
      {{ (isReplacing | async) ? 'REPLACING...' : 'REPLACE' }}
    </button>
    <button
      kendoButton
      id="cancel"
      (click)="cancel()"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel">
      CLOSE
    </button>
  </div>
</kendo-dialog-actions>
