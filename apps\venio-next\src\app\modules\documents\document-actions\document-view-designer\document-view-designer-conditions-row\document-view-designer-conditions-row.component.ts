import {
  ChangeDetectionStrategy,
  Component,
  computed,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  signal,
  SimpleChanges,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DropDownListModule,
  DropDownsModule,
} from '@progress/kendo-angular-dropdowns'
import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  ConditionStackManager,
  ClosestTextFinderService,
  DebounceTimer,
} from '@venio/util/utilities'
import {
  ConditionElement,
  ConditionRowChangeTypes,
  CONSTANTS,
  FieldOperator,
  GroupOperator,
  OperatorData,
  OperatorGroup,
  PrimaryValueType,
  SearchDataType,
  SearchField,
  SearchFieldModel,
  SizeType,
} from '@venio/shared/models/interfaces'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { DateInputsModule } from '@progress/kendo-angular-dateinputs'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'

interface ConditionElementRender {
  isSingleSelect: boolean
  isMultiSelect: boolean
  isBoolean: boolean
  isDate: boolean
  isNumber: boolean
  isText: boolean
}

@Component({
  selector: 'venio-document-view-designer-conditions-row',
  standalone: true,
  imports: [
    CommonModule,
    DropDownListModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    SvgLoaderDirective,
    DropDownsModule,
    DateInputsModule,
    TooltipsModule,
    IconsModule,
    LoaderModule,
  ],
  templateUrl: './document-view-designer-conditions-row.component.html',
  styleUrl: './document-view-designer-conditions-row.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentViewDesignerConditionsRowComponent implements OnChanges {
  private readonly conditionStackManager = new ConditionStackManager()

  public readonly commonActionTypes = CommonActionTypes

  public readonly infoSvgUrl = 'assets/svg/material_info_outline.svg'

  @Input()
  public condition: ConditionElement

  @Input()
  public fields: SearchField[]

  @Input()
  public searchField: SearchFieldModel

  public selectedField = signal<SearchField>(undefined)

  public generatedSyntax = signal<string>('')

  @Output()
  public readonly conditionRowChange = new EventEmitter<
    Partial<Record<ConditionRowChangeTypes, any>>
  >()

  public selectedOperator = signal<OperatorData>(undefined)

  public conditionElementRender = signal<ConditionElementRender>(undefined)

  public isSizeTypeVisible = computed(() =>
    CONSTANTS.fieldForSize.includes(this.selectedField()?.fieldName?.trim())
  )

  public controlType = computed(
    (): SearchDataType =>
      this.selectedField()?.searchDataType || SearchDataType.None
  )

  public groupOperators = [GroupOperator.AND, GroupOperator.OR]

  public operatorData = signal<OperatorData[]>([])

  public isPrimaryValueVisible = signal<boolean>(false)

  public isSecondaryValueVisible = signal<boolean>(false)

  public primaryDropdownValues = signal<PrimaryValueType[]>([])

  public ngOnChanges(changes: SimpleChanges): void {
    this.#computeChanges()
  }

  #computeChanges(): void {
    const fieldFound = this.fields.find(
      (c) => c.displayName === this.condition.fieldName
    )
    this.selectedField.set(fieldFound)

    this.#updateConditionElementRender()

    const operators = this.conditionStackManager.getFieldOperators(fieldFound)
    const foundOperator = operators.find(
      (o) => o.operator === this.condition.operator
    )
    this.operatorData.set(operators)
    this.selectedOperator.set(foundOperator)

    this.#computePrimaryValue()

    this.isPrimaryValueVisible.set(this.#computePrimaryVisibility())

    this.isSecondaryValueVisible.set(this.#computeSecondaryVisibility())
    this.generatedSyntax.set(
      this.conditionStackManager.convertConditionToString(this.condition)
    )
  }

  #computePrimaryVisibility(): boolean {
    const operator = this.selectedOperator()
    return operator?.group !== OperatorGroup.NULL
  }

  #computeSecondaryVisibility(): boolean {
    if (!this.selectedField()) return false
    const operator = this.selectedOperator()

    return operator?.displayName === 'BETWEEN' && operator?.group !== 'NULL'
  }

  #updateConditionElementRender(): void {
    // according to the search data type, we need to toggle the flags of conditionElementRender and use it in the UI
    const controlType = this.controlType()
    this.conditionElementRender.set({
      isSingleSelect:
        controlType === SearchDataType.SingleSelect ||
        controlType === SearchDataType.Boolean,
      isMultiSelect: controlType === SearchDataType.MultiSelect,
      isDate: controlType === SearchDataType.Date,
      isNumber:
        controlType === SearchDataType.Number ||
        controlType === SearchDataType.NumberSize,
      isText:
        controlType === SearchDataType.String ||
        controlType === SearchDataType.Dbstring,
      isBoolean: controlType === SearchDataType.Boolean,
    })
  }

  public fieldChange(displayName: string): void {
    const selectedField = this.fields.find((f) => f.displayName === displayName)
    const operatorData =
      this.conditionStackManager.getFieldOperators(selectedField)
    const data: ConditionElement = {
      id: this.condition.id,
      selectedField,
      fieldName: displayName,
      operator: operatorData[0]?.operator || ('' as FieldOperator),
      primaryValue: '',
      secondaryValue: '',
      sizeType: '' as SizeType,
    }
    const payload: Partial<Record<ConditionRowChangeTypes, ConditionElement>> =
      {
        [ConditionRowChangeTypes.UPDATE_CONDITION]: data,
      }
    this.conditionRowChange.emit(payload)

    this.selectedField.set(selectedField)
    this.generatedSyntax.set(
      this.conditionStackManager.convertConditionToString(data)
    )
  }

  public sizeChange(sizeType: SizeType): void {
    const data = {
      ...this.condition,
      sizeType,
    } as ConditionElement
    const payload: Partial<Record<ConditionRowChangeTypes, ConditionElement>> =
      {
        [ConditionRowChangeTypes.UPDATE_CONDITION]: data,
      }
    this.conditionRowChange.emit(payload)
    this.generatedSyntax.set(
      this.conditionStackManager.convertConditionToString(data)
    )
  }

  public operatorChange(operator: FieldOperator): void {
    const operatorData = this.operatorData().find(
      (c) => c.operator === operator
    )
    this.selectedOperator.set(operatorData)

    const data = {
      ...operatorData,
      primaryValue: '',
      secondaryValue: '',
      operator,
    } as OperatorData

    const payload: Partial<Record<ConditionRowChangeTypes, any>> = {
      [ConditionRowChangeTypes.UPDATE_CONDITION]: data,
    }
    this.conditionRowChange.emit(payload)

    this.isPrimaryValueVisible.set(this.#computePrimaryVisibility())

    this.isSecondaryValueVisible.set(this.#computeSecondaryVisibility())
    this.generatedSyntax.set(
      this.conditionStackManager.convertConditionToString(this.condition)
    )
  }

  @DebounceTimer(300)
  public valueChange(): void {
    // we have used the ngModel to directly bind the model change so simply returning the
    // new copy would be sufficient
    const payload: Partial<Record<ConditionRowChangeTypes, ConditionElement>> =
      {
        [ConditionRowChangeTypes.UPDATE_CONDITION]: {
          ...this.condition,
        },
      }
    this.conditionRowChange.emit(payload)
    this.generatedSyntax.set(
      this.conditionStackManager.convertConditionToString(this.condition)
    )
  }

  public actionClicked(
    actionType: CommonActionTypes,
    groupOperator = null
  ): void {
    const isAddGroup = actionType === CommonActionTypes.ADD && groupOperator
    const isAddCondition =
      actionType === CommonActionTypes.ADD && !groupOperator
    const isRemoveCondition =
      actionType === CommonActionTypes.DELETE && !groupOperator
    const conditionRowChangeType: ConditionRowChangeTypes = isAddGroup
      ? ConditionRowChangeTypes.ADD_GROUP
      : isAddCondition
      ? ConditionRowChangeTypes.ADD_CONDITION
      : isRemoveCondition
      ? ConditionRowChangeTypes.REMOVE_CONDITION
      : null
    if (!conditionRowChangeType) {
      return
    }

    const payload: Partial<Record<ConditionRowChangeTypes, any>> = {
      [conditionRowChangeType]:
        groupOperator || isAddCondition || isRemoveCondition,
    }
    this.conditionRowChange.emit(payload)
    this.generatedSyntax.set(
      this.conditionStackManager.convertConditionToString(this.condition)
    )
  }

  #setBooleanForPrimaryDropdown(): boolean {
    const { searchDataType } = this.selectedField()
    const isBoolean = searchDataType === SearchDataType.Boolean

    if (isBoolean) {
      this.primaryDropdownValues.set([{ value: 'True' }, { value: 'False' }])
    }

    return isBoolean
  }

  #setConstValueForPrimaryDropdown(): boolean {
    let hasSet = false
    const { fieldName } = this.selectedField()

    switch (fieldName.toLowerCase()) {
      case 'family_document_type':
        this.primaryDropdownValues.set(CONSTANTS.SEARCH_FAMILY_DOCUMENT_TYPES)
        hasSet = true
        break
      case 'default_fulltext_preference':
      case 'export_fulltext_preference':
      case 'fulltext_type':
        this.primaryDropdownValues.set(CONSTANTS.SEARCH_FULLTEXT_PREFERENCES)
        hasSet = true
        break
      case 'document_type':
        this.primaryDropdownValues.set(CONSTANTS.SEARCH_DOCUMENT_TYPES)
        hasSet = true
        break
    }
    return hasSet
  }

  #computePrimaryValue(): void {
    if (!this.selectedField()) return

    // exit early if the data type is boolean and we have set the value
    const hasBooleanValueSet = this.#setBooleanForPrimaryDropdown()
    if (hasBooleanValueSet) return

    const hasStaticValueSet = this.#setConstValueForPrimaryDropdown()
    // exit early if we have successfully set the found static value
    if (hasStaticValueSet) return

    const totallyDifferentFieldToLookFor = { 'detected language': 'languages' }

    const allSearchField = this.searchField
    const { displayName } = this.selectedField()

    const totallyDifferentFieldName =
      totallyDifferentFieldToLookFor[displayName.toLowerCase()]

    if (totallyDifferentFieldName) {
      this.primaryDropdownValues.set(
        (allSearchField[totallyDifferentFieldName] as string[])?.map(
          (value) => ({ value } as PrimaryValueType)
        )
      )
      return
    }
    const textFinder = new ClosestTextFinderService()
    textFinder
      .findClosestMatch(Object.keys(allSearchField), displayName)
      .then((property: string) => {
        textFinder.terminate()

        if (!property) return

        this.primaryDropdownValues.set(
          (allSearchField[property] as string[])?.map(
            (value) => ({ value } as PrimaryValueType)
          )
        )
      })
  }
}
