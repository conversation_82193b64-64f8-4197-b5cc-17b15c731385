import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import {
  DynamicFolderModel,
  FolderModel,
  InitialSearchResultParameter,
  SearchResponseModel,
} from '../../models/interfaces'
import { SearchState, SEARCH_FEATURE_KEY } from './search.reducer'

export const getSearchState =
  createFeatureSelector<SearchState>(SEARCH_FEATURE_KEY)

export const getStateOfSearchState = <T extends keyof SearchState>(
  stateKey: T
): MemoizedSelector<object, SearchState[T], unknown> =>
  createSelector(getSearchState, (state: SearchState) => state[stateKey])

export const getSearchTempTables = createSelector(
  getStateOfSearchState('searchResponse'),
  (searchResponse: SearchResponseModel) => {
    return searchResponse?.tempTables
  }
)

export const getSearchInitialSearchResultParameters = createSelector(
  getStateOfSearchState('searchResponse'),
  (searchResponse: SearchResponseModel) => {
    return searchResponse?.searchResultIntialParameters
  }
)

export const getReviewSetBatchId = createSelector(
  getSearchInitialSearchResultParameters,
  (searchResultParameters: InitialSearchResultParameter) => {
    return searchResultParameters?.batchId
  }
)

export const getTotalHitCount = createSelector(
  getSearchInitialSearchResultParameters,
  (searchParameters: InitialSearchResultParameter) => {
    return searchParameters?.totalHitCount
  }
)

export const getDynamicFolderSearchScope = createSelector(
  getStateOfSearchState('dynamicFolderSearchScope'),
  (dynamicFolderSearchScope: DynamicFolderModel) => {
    return dynamicFolderSearchScope
  }
)

export const getStaticFolderSearchScope = createSelector(
  getStateOfSearchState('staticFolderSearchScope'),
  (staticFolderSearchScope: FolderModel) => {
    return staticFolderSearchScope
  }
)
