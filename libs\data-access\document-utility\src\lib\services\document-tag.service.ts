import { HttpClient, HttpParams } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import { cloneDeep } from 'lodash'
import {
  DocumentProjectTagRequestModel,
  DocumentTagRequestModel,
  FileTaggedRequestModel,
  SaveTagRequestModel,
} from '../models/interfaces/document-tag.model'
import { ActivatedRoute } from '@angular/router'
import { StringUtils } from '@venio/util/utilities'
import {
  SearchFacade,
  ViewTagRuleConflictModel,
} from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'

@Injectable({
  providedIn: 'root',
})
export class DocumentTagService {
  private get _apiUrl(): string {
    return environment.apiUrl
  }

  private searchFacade = inject(SearchFacade)

  private ruleIdPatternList = toSignal(
    this.searchFacade.selectTagRuleIdPatternList$
  )

  constructor(
    private http: HttpClient,
    private activatedRoute: ActivatedRoute
  ) {}

  public fetchTags$(
    projectId: number,
    isExternalUser: boolean,
    isGetAllParentTag: boolean,
    reviewSetId: number
  ): Observable<any> {
    if (reviewSetId > 0)
      return this.http.get(
        this._apiUrl + `tags/project/${projectId}/reviewset/${reviewSetId}`
      )
    return this.http.get(this._apiUrl + `tags/project/${projectId}`, {
      params: new HttpParams()
        .set('isExternalUser', String(isExternalUser))
        .set('isGetAllParentTag', String(isGetAllParentTag)),
    })
  }

  public fetchDocumentTags$(
    documentTagsRequest: DocumentTagRequestModel
  ): Observable<any> {
    const request = cloneDeep(documentTagsRequest)

    return this.http.post(this._apiUrl + 'file/tags', request)
  }

  public fetchProfileCategoryByTagGroupId$(
    tagGroupId: number,
    projectId: number
  ): Observable<any> {
    return this.http.get(
      `${this._apiUrl}file/tags/project/${projectId}/CALProfileTrainingTags`,
      {
        params: new HttpParams().set('tagGroupId', tagGroupId),
      }
    )
  }

  public fetchTagSettings$(projectId: number): Observable<any> {
    return this.http.get(`${this._apiUrl}tagsettings/project/${projectId}`)
  }

  public updateFileStatus$(
    fileTaggedRequestModel: FileTaggedRequestModel,
    projectId: number
  ): Observable<any> {
    return this.http.post(
      `${this._apiUrl}file/tags/project/${projectId}/UpdateFileStatus`,
      fileTaggedRequestModel,
      {}
    )
  }

  public saveDocumentTags$(
    saveTagRequestModel: SaveTagRequestModel
  ): Observable<any> {
    // const request = this.jsonConvert.serialize(
    //   saveTagRequestModel,
    //   SaveTagRequestModel
    // )
    const projectId = saveTagRequestModel.ProjectId
    return this.http.post(
      `${this._apiUrl}file/tags/project/${projectId}/save`,
      saveTagRequestModel,
      {}
    )
  }

  public fetchTagRuleList$(projectId: number): Observable<any> {
    return this.http.get(
      `${this._apiUrl}tag-rule/project/${projectId}/tag-rule-list`
    )
  }

  public fetchTagRuleDescription$(projectId: number): Observable<any> {
    return this.http.get(`${this._apiUrl}tag-rule/project/${projectId}/rules`)
  }

  public getConflictedTagRules$(
    projectId: number,
    fileId: number
  ): Observable<any> {
    return this.http.get(
      `${this._apiUrl}/tag-rule/project/${projectId}/file/${fileId}/conflicts`
    )
  }

  public isReviewForTagRuleConflicted(): boolean {
    const strModuleData = this.activatedRoute.snapshot.queryParams['moduleData']
    if (StringUtils.isNullOrEmpty(strModuleData)) return false

    const jsondata = atob(strModuleData)
    const tagRuleData: ViewTagRuleConflictModel = JSON.parse(jsondata)
    return tagRuleData.module === 'VIEW_TAG_RULE_CONFILICT'
  }

  public getTagRuleData(): ViewTagRuleConflictModel | null {
    // Safely get ruleIds from ruleIdPatternList, defaulting to an empty array
    const ruleIdPatternList = this.ruleIdPatternList()
    const ruleIds = ruleIdPatternList ? ruleIdPatternList.ruleIds : []

    // Retrieve moduleData from query parameters
    const strModuleData = this.activatedRoute.snapshot.queryParams['moduleData']

    // If both strModuleData and ruleIds are empty, return null
    if (StringUtils.isNullOrEmpty(strModuleData) && ruleIds.length === 0) {
      return null
    }

    let tagRuleData: ViewTagRuleConflictModel | null = null

    if (!StringUtils.isNullOrEmpty(strModuleData)) {
      try {
        // Decode the base64 string
        const decodedData = atob(strModuleData)
        // Parse the JSON data
        tagRuleData = JSON.parse(decodedData)
      } catch {
        tagRuleData = null
      }
    }

    // Return tagRuleData if available
    if (tagRuleData) {
      console.log('tagRuleData: ', tagRuleData)
      this.searchFacade.updateTagRuleIdPatternList({
        ruleIds: tagRuleData.conflictTagRuleIds,
        syntax: [],
      })
      return tagRuleData
    }

    // If ruleIds are available, return them wrapped in a ViewTagRuleConflictModel
    if (ruleIds.length > 0) {
      return { conflictTagRuleIds: ruleIds } as ViewTagRuleConflictModel
    }

    // If no data is available, return null
    return null
  }

  public fetchDocumentProjectTagGroups$(
    documentProjectTagRequest: DocumentProjectTagRequestModel
  ): Observable<any> {
    const request = cloneDeep(documentProjectTagRequest)

    return this.http.post(
      this._apiUrl + 'file/tags/document-tag-groups',
      request
    )
  }
}
