import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  ViewChild,
  ViewContainerRef,
  OnInit,
  OnDestroy,
  Input,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { GridDataResult, GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { ActivatedRoute } from '@angular/router'
import { saveAs } from '@progress/kendo-file-saver'
import {
  DocSelectionTypeEnum,
  DocumentsFacade,
  SearchFacade,
  RSMFCreationFacade,
  FieldMapping,
  FieldCategory,
  RsmfConversion,
  Mapping,
  statusMap,
  TempTableResponseModel,
  RsmfMapping,
} from '@venio/data-access/review'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import {
  Subject,
  take,
  takeUntil,
  filter,
  combineLatest,
  interval,
  switchMap,
  tap,
  takeWhile,
} from 'rxjs'
import {
  NotificationModule,
  NotificationService,
  Type as NotificationType,
} from '@progress/kendo-angular-notification'
import { FormsModule } from '@angular/forms'
import {
  LabelSettings,
  ProgressBarModule,
} from '@progress/kendo-angular-progressbar'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-rsmf-creation-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    TooltipsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    UiPaginationModule,
    NotificationModule,
    SvgLoaderDirective,
    ProgressBarModule,
    DropDownsModule,
    TooltipsModule,
    IndicatorsModule,
    DynamicHeightDirective,
  ],
  templateUrl: './rsmf-creation-dialog.component.html',
  styleUrl: './rsmf-creation-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RsmfCreationDialogComponent implements OnInit, OnDestroy {
  @Input() public dialogRef!: DialogRef

  @ViewChild('appendNotification', { read: ViewContainerRef, static: false })
  public appendTo: ViewContainerRef

  public dialogTitle = 'Generate RSMF'

  public selectedSource = 'FACEBOOK'

  public toDestroy$: Subject<void> = new Subject<void>()

  public showCreateRSMFTab = true

  public selectedTabIndex = 0

  public selectionTypeEnum!: DocSelectionTypeEnum

  public fileIdList: number[] = []

  public tempSearchResultTable!: string

  public columnWidth!: number

  public source: { displayName: string; value: string }[] = [
    { displayName: 'Slack', value: 'SLACK' },
    { displayName: 'Facebook', value: 'FACEBOOK' },
    { displayName: 'Cellebrite', value: 'CELLEBRITE' },
    { displayName: 'Twitter', value: 'TWITTER' },
  ]

  public defaultSouceValue: { displayName: string; value: null | string } = {
    displayName: 'Select Source',
    value: null,
  }

  public venioFields!: FieldCategory

  public masterGridDataRSMF: RsmfMapping[]

  public gridDataRSMF: FieldMapping[] = []

  public showConversionProgress = false

  public showConversionCompleted = false

  public showMappingGrid = true

  public rsmfJobId!: number

  public commonFields!: object

  public searchTempTable: TempTableResponseModel

  public isBatchSelected = false

  public jobId: number

  public conversionCompletion = 0

  public rsmfStatusList: GridDataResult = { data: [], total: 0 }

  public createBtnDisabled = false

  public rsmfStatusData: any

  public disableDownloadArchiveBtn = false

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public tabStatus = 0

  public statusMap = statusMap

  public isDeletingRSMF = this.rsmfCreationFacade.getIsDeletingRSMF$

  public isRSMFStatusLoading = this.rsmfCreationFacade.isFetchingRSMFStatus$

  public isCreatingRSMF$ = this.rsmfCreationFacade.isCreatingRSMF$

  public rsmfSvgUrl = 'assets/svg/rsmf-icon-light.svg'

  public downloadSvgUrl = 'assets/svg/download.svg'

  public deleteSvgUrl = 'assets/svg/icon_material_delete.svg'

  public refreshSvgUrl = 'assets/svg/refresh-svgrepo-com.svg'

  public svgIconForGridControls: {
    actionType: string
    iconPath: string
    color: string
    hoverColor: string
    hoverBtnBg: string
  }[] = [
    {
      actionType: 'Download RSMF',
      iconPath: this.downloadSvgUrl,
      color: '',
      hoverColor: '#fff',
      hoverBtnBg: '#1EBADC ',
    },
    {
      actionType: 'Delete',
      iconPath: this.deleteSvgUrl,
      color: '#979797',
      hoverColor: '#fff',
      hoverBtnBg: '#ED7425',
    },
  ]

  public label: LabelSettings = {
    visible: true,
    format: 'percent',
    position: 'end',
  }

  //Pagination Config
  public gridDataStatus: GridDataResult = { data: [], total: 0 }

  public currentPageStatus = 1

  public pageSizeStatus = 10

  public skipStatus = 0

  // Notification Config
  public content = ''

  public type: NotificationType = { style: 'error', icon: true }

  public hideAfter = 3500

  public width = 520

  public filteredVenioFields: { [key: string]: any[] } = {}

  constructor(
    private dialogService: DialogService,
    private notificationService: NotificationService,
    private activatedRoute: ActivatedRoute,
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private cdr: ChangeDetectorRef,
    private rsmfCreationFacade: RSMFCreationFacade,
    private elRef: ElementRef
  ) {}

  public ngOnInit(): void {
    this.#loadGrid()
    this.#loadGridData()
  }

  /**
   * Construct & Loads the grid.
   */
  #loadGrid(): void {
    const gridElement = this.elRef.nativeElement.querySelector('kendo-grid')
    if (gridElement) {
      const totalGridWidth = gridElement.clientWidth
      this.columnWidth = totalGridWidth / 2
    }
  }

  /**
   * Loads the grid data for RSMF creation.
   */
  #loadGridData(): void {
    this.rsmfCreationFacade.fetchRSMFieldMapping(this.projectId)
    this.rsmfCreationFacade.fetchRSMFieldMappingSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        if (res) {
          this.masterGridDataRSMF = res?.data
          this.gridDataRSMF = this.masterGridDataRSMF.find(
            (x) => x.source.toLowerCase() === this.selectedSource.toLowerCase()
          )?.mappings

          this.commonFields = this.#generateCommonFields(this.gridDataRSMF)
          this.venioFields = this.#generateVenioFields(this.gridDataRSMF)
          this.filteredVenioFields = { ...this.venioFields }
          this.cdr.detectChanges()
        }
      })

    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getSearchTempTables$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(
        ([isBatchSelected, selectedDocs, unselectedDocs, searchTempTables]) => {
          this.searchTempTable = searchTempTables
          this.isBatchSelected = isBatchSelected
          this.fileIdList = isBatchSelected ? unselectedDocs : selectedDocs
          this.selectionTypeEnum =
            isBatchSelected && unselectedDocs.length
              ? DocSelectionTypeEnum.AllFilesExceptSelected
              : isBatchSelected
              ? DocSelectionTypeEnum.AllFiles
              : DocSelectionTypeEnum.SelectedFilesOnly
          this.tempSearchResultTable = searchTempTables.searchResultTempTable
        }
      )
  }

  /**
   * Determines if an item is disabled in the dropdown.
   * @param itemArgs - The item arguments containing the data item.
   * @returns True if the item is disabled, otherwise false.
   */
  public itemDisabled(itemArgs: { dataItem: any }): boolean {
    return itemArgs.dataItem.value === null
  }

  /**
   * Handles the change event for Venio field selection.
   * @param rowIndex - The index of the row in the grid.
   * @param selectedValue - The selected value from the dropdown.
   */
  public onVenioFieldChange(rowIndex: number, selectedValue: any): void {
    this.gridDataRSMF[rowIndex].rsmfField = selectedValue
  }

  public handleFilterChange(filter: string, fieldKey: string): void {
    // Get the original data from venioFields
    const originalData = this.venioFields[fieldKey] || []

    // Filter the data based on the user's input
    this.filteredVenioFields[fieldKey] = originalData.filter((item) =>
      item.text.toLowerCase().includes(filter.toLowerCase())
    )
  }

  /**
   * Checks if the given tab index is currently selected.
   * @param tabIndex - The index of the tab to check.
   * @returns True if the tab is selected, otherwise false.
   */
  public isSelected(tabIndex: number): boolean {
    return this.selectedTabIndex === tabIndex
  }

  /**
   * Handles the tab selection event.
   * @param e - The event object containing the selected tab index.
   */
  public onSelect(e: any): void {
    this.tabStatus = e.index
    e.index === 0 ? this.#loadGridData() : this.#fetchRSMFStatus()
  }

  /**
   * Generates common fields from the grid data.
   * @param data - The grid data containing field mappings.
   * @returns An object containing common fields grouped by category.
   */
  #generateCommonFields(data: FieldMapping[]): FieldCategory {
    return data?.reduce((acc, { rsmfField }) => {
      const category = rsmfField.split('.')[0]
      acc[category] = acc[category] || []
      acc[category].push({ text: rsmfField, value: rsmfField })
      return acc
    }, {} as FieldCategory)
  }

  /**
   * Generates Venio fields from the grid data.
   * @param data - The grid data containing field mappings.
   * @returns An object containing Venio fields grouped by category.
   */
  #generateVenioFields(data: FieldMapping[]): FieldCategory {
    const venioFields: any = {}
    data?.forEach(({ venioField, rsmfField }) => {
      const category = rsmfField.split('.')[0]
      venioFields[venioField] =
        venioFields[venioField] ||
        this.commonFields[category].filter((f) => f.value !== rsmfField)
      venioFields[venioField].push({ text: rsmfField, value: rsmfField })
    })
    return venioFields
  }

  /**
   * Fetches the RSMF status for the current project.
   */
  #fetchRSMFStatus(): void {
    this.rsmfCreationFacade.fetchRSMFStatus(this.projectId)
    this.rsmfCreationFacade.fetchRSMFStatusSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        if (res?.data?.length > 0) {
          const rsmfStatusList = res?.data
          this.rsmfStatusData = res?.data
          if (this.jobId) {
            this.disableDownloadArchiveBtn = !this.#isJobIdExist(this.jobId)
          }
          this.rsmfStatusList = {
            data: rsmfStatusList.slice(
              this.skipStatus,
              this.skipStatus + this.pageSizeStatus
            ),
            total: rsmfStatusList.length,
          }
        } else {
          const rsmfStatusList = []
          this.rsmfStatusData = []
          if (this.jobId) {
            this.disableDownloadArchiveBtn = !this.#isJobIdExist(this.jobId)
          }
          this.rsmfStatusList = {
            data: rsmfStatusList?.slice(
              this.skipStatus,
              this.skipStatus + this.pageSizeStatus
            ),
            total: rsmfStatusList.length,
          }
        }
        this.cdr.markForCheck()
      })
  }

  /**
   * Handles the page change event for pagination.
   * @param args - The page arguments containing the new page number and page size.
   */
  public pageChanged(args: PageArgs): void {
    this.currentPageStatus = args.pageNumber
    this.skipStatus = (args.pageNumber - 1) * args.pageSize
    this.#fetchRSMFStatus()
  }

  /**
   * Handles the page size change event for pagination.
   * @param args - The page arguments containing the new page number and page size.
   */
  public pageSizeChanged(args: PageArgs): void {
    this.pageSizeStatus = args.pageSize
    this.skipStatus = (args.pageNumber - 1) * args.pageSize
    this.#fetchRSMFStatus()
  }

  /**
   * Closes the dialog with the given status.
   * @param status - The status to close the dialog with.
   */
  public close(status: string): void {
    this.#resetRSMFCreationState()
    this.dialogRef.close()
  }

  /**
   * Shows a notification with the given content and type.
   * @param content - The content of the notification.
   * @param type - The type of the notification.
   * @param delay - The delay before the notification is hidden.
   * @param width - The width of the notification.
   */
  private showNotification(
    content: string,
    type: NotificationType,
    delay: number,
    width: number
  ): void {
    this.notificationService.show({
      appendTo: this.appendTo,
      content: content,
      animation: { type: 'fade', duration: 300 },
      type: type, // Use Kendo Notification Type
      cssClass: 'v-custom-save-notification v-custom-notification-multiline',
      position: { horizontal: 'center', vertical: 'top' },
      hideAfter: delay,
      closable: false,
    })
  }

  /**
   * Handles the action click event for grid controls.
   * @param actionType - The type of action to perform.
   * @param dataItem - The data item associated with the action.
   */
  public browseActionClicked(actionType: string, jobId: number): void {
    switch (actionType) {
      case 'Download RSMF': {
        this.downloadRSMFArchive(jobId)
        break
      }
      case 'Delete': {
        this.#confirmRSMFDeletion(jobId)
        break
      }
    }
  }

  /**
   * Downloads the RSMF archive for the given job ID.
   * @param jobId - The job ID to download the RSMF archive for.
   */
  public downloadRSMFArchive(jobId?: number): void {
    const rsmfJobId = jobId || this.jobId
    if (this.#isJobIdExist(rsmfJobId)) {
      this.disableDownloadArchiveBtn = false
      this.rsmfCreationFacade.downloadRSMFInfo(rsmfJobId, this.projectId)
      this.rsmfCreationFacade.getDownloadRSMFInfoSuccessResponse$
        .pipe(
          filter((res: ResponseModel) => !!res?.data),
          take(1)
        )
        .subscribe((res: ResponseModel) => {
          if (res?.data) {
            this.#downLoadFile(
              res?.data?.buffer,
              res?.data?.mimeType,
              res?.data?.fileName
            )
            this.#showNotification('Downloaded Successfully', {
              style: 'success',
            })
          }
        })

      this.rsmfCreationFacade.getDownloadRSMFInfoFailureResponse$
        .pipe(
          filter((res: ResponseModel) => !!res?.data),
          take(1)
        )
        .subscribe((res: ResponseModel) => {
          if (res?.data) {
            this.#showNotification('Download Failed Rsmf Archive.', {
              style: 'error',
            })
          }
        })
    } else {
      this.disableDownloadArchiveBtn = true
      this.cdr.markForCheck()
    }
  }

  #isJobIdExist(jobId: number): boolean {
    if (this.rsmfStatusData?.length > 0) {
      return this.rsmfStatusData?.some((res) => res?.jobId === jobId)
    }
    return false
  }

  /**
   * Confirms the deletion of an RSMF conversion.
   * @param jobId - The job ID to confirm deletion for.
   */
  #confirmRSMFDeletion(jobId: number): void {
    const dialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
    })

    this.#setDialogInput(dialogRef.content.instance)

    dialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => this.#performTaskAfterConfirmation(jobId))
  }

  #showNotification(content: string, type: NotificationType): void {
    this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })
  }

  /**
   * Sets the input for the confirmation dialog.
   * @param instance - The instance of the confirmation dialog component.
   */
  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Confirm RSMF Conversion Deletion'
    instance.message = `Are you sure you want to delete ?`
  }

  /**
   * Performs the task after confirmation of RSMF deletion.
   * @param jobId - The job ID to delete.
   */
  #performTaskAfterConfirmation(jobId: number): void {
    this.rsmfCreationFacade.deleteRSMF(jobId, this.projectId)
    let hasNotified = false

    const deleteSubscription =
      this.rsmfCreationFacade.deleteRSMFSuccessResponse$
        .pipe(takeUntil(this.toDestroy$))
        .subscribe((res) => {
          if (res && !hasNotified) {
            this.#fetchRSMFStatus()
            this.content = res?.message
            this.type = { style: 'success', icon: true }
            this.showNotification(
              this.content,
              this.type,
              this.hideAfter,
              this.width
            )

            hasNotified = true
            this.#resetRSMFDeleteState()
            deleteSubscription?.unsubscribe()
          }
        })

    this.rsmfCreationFacade.deleteRSMFFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.content = error.message
        this.type = { style: 'error', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.#resetRSMFDeleteState()
      })
  }

  /**
   * Downloads a file with the given data, file type, and file name.
   * @param data - The data to download.
   * @param fileType - The MIME type of the file.
   * @param fileName - The name of the file.
   */
  #downLoadFile(data: any, fileType: string, fileName: string): void {
    const byteCharacters = window.atob(data)
    const byteArrays = []
    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512)
      const byteNumbers = new Array(slice.length)
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      byteArrays.push(byteArray)
    }

    const blob = new Blob(byteArrays, { type: fileType })
    saveAs(blob, fileName)
  }

  /**
   * Generates a file name for the RSMF conversion.
   * @returns The generated file name.
   */
  #generateFileName(): string {
    const timestamp = new Date().getTime()
    return `RSMF_${timestamp}`
  }

  /**
   * Creates an RSMF conversion with the current settings.
   */
  public createRSMFConvsersion(): void {
    this.cdr.detectChanges()
    this.createBtnDisabled = true
    const payload: RsmfConversion = {
      ProjectId: this.projectId,
      RsmfConversionName: this.#generateFileName(),
      BaseLocation: '',
      SocialMediaType: this.selectedSource,
      Mappings: this.#convertGridDataRsmfToMapping(this.gridDataRSMF),
      DeleteRsmf: false,
      SearchTempTable: this.tempSearchResultTable,
      IsBatchSelected: this.isBatchSelected,
      SelectionType: this.selectionTypeEnum.toString(),
      FileIdList: this.fileIdList.join(','),
      FieldSelection: '',
    }

    this.rsmfCreationFacade.createRSMF(payload, this.projectId)
    const createRSMFresponseSubscription =
      this.rsmfCreationFacade.createRSMFSuccessResponse$.subscribe((res) => {
        if (res !== null) {
          this.jobId = res?.data

          if (this.jobId !== -1 && this.jobId !== -2) {
            this.#monitorRSMFConversionProgress()
            this.showMappingGrid = false
            this.showConversionProgress = true

            this.#showNotification('Queued Successfully For Conversion', {
              style: 'success',
            })
          } else {
            this.createBtnDisabled = false
            this.#showNotification('Invalid File Selection For Conversion', {
              style: 'error',
            })
            this.rsmfCreationFacade.resetRSMFCreationState([
              'createRSMFSuccessResponse',
            ])
          }

          // Unsubscribe after handling the first emission
          createRSMFresponseSubscription.unsubscribe()
        }
      })
  }

  /**
   * Monitors the progress of the RSMF conversion.
   */
  #monitorRSMFConversionProgress(): void {
    let showNotification = false
    const maxAttempts = 100

    interval(4000)
      .pipe(
        takeWhile(
          (attempt) =>
            attempt < maxAttempts &&
            this.conversionCompletion < 100 &&
            !showNotification
        ),
        tap(() => {
          this.rsmfCreationFacade.fetchRSMFStatusDetail(
            this.jobId,
            this.projectId
          )
        }),
        switchMap(
          () => this.rsmfCreationFacade.fetchRSMFStatusDetailSuccessResponse$
        ),
        tap((res) => {
          if (res?.data) {
            this.conversionCompletion = Math.min(
              100,
              Math.max(
                0,
                Math.floor(
                  ((res?.data?.completedCount + res?.data?.failedCount) /
                    res.data.totalCount) *
                    100
                )
              )
            )
            this.cdr.markForCheck()
            if (this.conversionCompletion === 100 && !showNotification) {
              showNotification = true
              this.content = 'RSMF Conversion successful.'
              this.type = { style: 'success', icon: true }
              this.showNotification(
                this.content,
                this.type,
                this.hideAfter,
                this.width
              )
              this.showConversionProgress = false
              this.showConversionCompleted = true
              this.#fetchRSMFStatus()
            }
          }
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe()
  }

  /**
   * Converts the grid data to a mapping format.
   * @param gridDataRsmf - The grid data containing field mappings.
   * @returns An array of mappings.
   */
  #convertGridDataRsmfToMapping(gridDataRsmf: FieldMapping[]): Mapping[] {
    return gridDataRsmf.map((data) => ({
      VenioField: data.venioField,
      RsmfField: data.rsmfField,
    }))
  }

  /**
   * Refreshes the RSMF status.
   */
  public onRefreshClick(): void {
    this.#fetchRSMFStatus()
  }

  public onSourceChange(selectedValue: string): void {
    this.selectedSource = selectedValue
    this.gridDataRSMF = this.masterGridDataRSMF.find(
      (x) => x.source.toLowerCase() === this.selectedSource.toLowerCase()
    )?.mappings
    this.commonFields = this.#generateCommonFields(this.gridDataRSMF)
    this.venioFields = this.#generateVenioFields(this.gridDataRSMF)
    this.filteredVenioFields = { ...this.venioFields }
    this.cdr.detectChanges()
  }

  #resetRSMFDeleteState(): void {
    this.rsmfCreationFacade.resetRSMFCreationState([
      'deleteRSMFSuccessResponse',
      'deleteRSMFFailureResponse',
      'isDeletingRSMF',
    ])
  }

  #resetRSMFCreationState(): void {
    this.rsmfCreationFacade.resetRSMFCreationState([
      'createRSMFSuccessResponse',
      'createRSMFFailureResponse',
      'isCreatingRSMF',
      'downloadRSMFInfoSuccessResponse',
      'downloadRSMFInfoFailureResponse',
      'isDownloadingRSMFInfo',
      'fetchRSMFStatusSuccessResponse',
      'fetchRSMFStatusFailureResponse',
      'isFetchingRSMFStatus',
      'fetchRSMFStatusDetailSuccessResponse',
      'fetchRSMFStatusDetailFailureResponse',
      'isFetchingRSMFStatusDetail',
      'fetchRSMFieldMappingSuccessResponse',
      'fetchRSMFieldMappingFailureResponse',
      'isFetchingRSMFieldMapping',
      'deleteRSMFSuccessResponse',
      'deleteRSMFFailureResponse',
      'isDeletingRSMF',
    ])
  }

  /**
   * Cleans up subscriptions when the component is destroyed.
   */
  public ngOnDestroy(): void {
    this.#resetRSMFCreationState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
