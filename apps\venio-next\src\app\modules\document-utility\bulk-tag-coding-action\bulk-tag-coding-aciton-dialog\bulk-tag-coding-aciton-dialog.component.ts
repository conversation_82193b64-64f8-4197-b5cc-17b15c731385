import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  ViewChild,
  signal,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ButtonGroupModule,
  ButtonModule,
} from '@progress/kendo-angular-buttons'
import {
  DialogModule,
  DialogRef,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import {
  SelectEvent,
  TabStripComponent,
  TabStripModule,
} from '@progress/kendo-angular-layout'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import {
  Subject,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil,
} from 'rxjs'
import {
  DataAccessDocumentUtilityModule,
  DocumentCodingFacade,
  DocumentTagFacade,
  ReviewPanelFacade,
} from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  ReviewSetStateService,
  UserRights,
} from '@venio/data-access/review'
import {
  NotificationModule,
  NotificationService,
  Type as KendoNotificationType,
} from '@progress/kendo-angular-notification'
import { ActivatedRoute } from '@angular/router'
import { DocumentTagUtilityService } from '../../utility-services/document-tag-utility'
import {
  CommonActionTypes,
  PageControlActionType,
  ShortcutKeyActions,
  ShortcutKeyBindings,
  UtilityPanelType,
} from '@venio/shared/models/constants'
import { SVGIcon, chevronLeftIcon } from '@progress/kendo-svg-icons'
import { CodingFacade, TagsFacade } from '@venio/data-access/common'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { LabelModule } from '@progress/kendo-angular-label'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { TooltipModule, TooltipsModule } from '@progress/kendo-angular-tooltip'
import { UiPaginationModule } from '@venio/ui/pagination'
import { GridModule } from '@progress/kendo-angular-grid'
import { Callback, ShortcutManager } from '@venio/util/utilities'
import { ConfirmationDialogService } from '../../../../services/confirmation-dialog-service'
import { WindowManagerService } from '../../../../services/window.manager.service'
import {
  WindowMessageType,
  WindowMessengerService,
} from '../../../../services/window.messenger.service'
import { AppIdentitiesTypes } from '@venio/data-access/iframe-messenger'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { CodingSearchComponent } from '../../tag-coding/document-coding/coding-search/coding-search.component'
import { DocumentCodingComponent } from '../../tag-coding/document-coding/document-coding/document-coding.component'

@Component({
  selector: 'venio-bulk-tag-coding-aciton-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ButtonGroupModule,
    ButtonModule,
    DialogModule,
    DropDownListModule,
    TabStripModule,
    NotificationModule,
    SvgLoaderDirective,
    LoaderModule,
    DataAccessDocumentUtilityModule,
    LabelModule,
    InputsModule,
    TooltipsModule,
    UserGroupRightCheckDirective,
    UiPaginationModule,
    GridModule,
    TooltipModule,
    CodingSearchComponent,
    DocumentCodingComponent,
  ],
  providers: [DocumentTagUtilityService],
  templateUrl: './bulk-tag-coding-aciton-dialog.component.html',
  styleUrls: ['./bulk-tag-coding-aciton-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkTagCodingAcitonDialogComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @Input()
  public codingFieldInput: EventEmitter<CommonActionTypes>

  @ViewChild('mainTabStrip')
  public mainTabStrip: TabStripComponent

  public readonly toDestroy$ = new Subject<void>()

  public readonly rights = UserRights

  private shortcutManager: ShortcutManager

  public opened = false

  private readonly defaultTitle = 'Bulk Tags & Coding'

  public dialogTitle = this.defaultTitle

  public isTagCommentValidationRequired = false

  public isValidCodingForm = false

  public isSavingData = signal<boolean>(false)

  public isTagFormValid = signal<boolean>(false)

  public isTagAddUpdate = signal<boolean>(false)

  public isCodingFieldAddUpdate = signal<boolean>(false)

  public isTagAndCodingFieldUpdate = signal<boolean>(false)

  public isDocumentCodingComponentLoaded = signal<boolean>(false)

  public isCodingFormValid = new EventEmitter<boolean>()

  public isTagRuleListLoaded$ =
    this.documentTagFacade.selectIsTagRuleListLoaded$

  public tagCommentValidationMessage =
    'Please enter comments for required tags.'

  public documentTagComponent = import('@venio/feature/tag-email-thread').then(
    (m) => m.DocumentTagComponent
  )

  public isConflictDialog = false

  public tagSearchComponent = import('@venio/feature/tag-email-thread').then(
    (m) => m.TagSearchComponent
  )

  public lazyTagFormComp = import('@venio/feature/tag-email-thread').then(
    (m) => m.TagFormComponent
  )

  public documentCodingComponent = import(
    '../../tag-coding/document-coding/document-coding/document-coding.component'
  ).then((m) => m.DocumentCodingComponent)

  public codingSearchComponent = import(
    '../../tag-coding/document-coding/coding-search/coding-search.component'
  ).then((m) => m.CodingSearchComponent)

  public codingFieldFormComp = import(
    '../../edit-action/coding-form/coding-form.component'
  ).then((m) => m.CodingFormComponent)

  /**
   * Container placeholder for Multivalue Coding Field
   */
  public multiValueCodingFieldContainer = import(
    '../../tag-coding/document-coding/multivalue-coding-field/multivalue-coding-field-container/multivalue-coding-field-container.component'
  ).then((m) => m.MultivalueCodingFieldContainerComponent)

  public bulkCodingList = import(
    '../../tag-coding/document-coding-list/document-coding-list.component'
  ).then((m) => m.DocumentCodingListComponent)

  public tagRuleInfoComp: Promise<Type<unknown>>

  public isTagAndCodingVisible = false

  public formMessage = ''

  public isTagTab = signal(true)

  public isCodingTab = signal(false)

  public isCodingDataValid = signal(false)

  public leftSvg: SVGIcon = chevronLeftIcon

  public svgIconForPageControls = [
    {
      actionType: PageControlActionType.FIRST_PAGE,
      actionText: PageControlActionType.FIRST_PAGE_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-first-page.svg',
    },
    {
      actionType: PageControlActionType.PREV_PAGE,
      actionText: PageControlActionType.PREV_PAGE_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-prev-page.svg',
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      actionText: PageControlActionType.NEXT_PAGE_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
    },
    {
      actionType: PageControlActionType.LAST_PAGE,
      actionText: PageControlActionType.LAST_PAGE_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-last-page.svg',
    },
  ]

  private reviewSetId: number

  constructor(
    private documentsFacade: DocumentsFacade,
    private documentTagFacade: DocumentTagFacade,
    private documentCodingFacade: DocumentCodingFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private codingFacade: CodingFacade,
    private tagsFacade: TagsFacade,
    private documentTagUtilityService: DocumentTagUtilityService,
    private messengerService: WindowMessengerService,
    private windowManager: WindowManagerService,
    private notificationService: NotificationService,
    private confirmationDialogService: ConfirmationDialogService,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private dialogService: DialogService,
    private reviewSetState: ReviewSetStateService,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public browseActionClicked(
    actionType: any //'FIRST_PAGE' | 'NEXT_PAGE' | 'PREV_PAGE' | 'LAST_PAGE'
  ): void {
    switch (actionType) {
      case 'FIRST_PAGE':
        // Invoke methods
        break
      case 'NEXT_PAGE':
        // Invoke methods
        break
      case 'PREV_PAGE':
        // Invoke methods
        break
      case 'LAST_PAGE':
        // Invoke methods
        break
    }
  }

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.#initReviewSetId()
    this.#fetchTagTree()
    this.#selectIsBulkCodingLoaded()
    this.#getSelectedDocuments()
    this.upateIsBulkDocument(true)
    this.#selectTagCodingResponses()
    this.#selectIsCodingDataValid()
    this.#fetchDocumentTagCommentValidation()
    this.openDialog()
  }

  public ngAfterViewInit(): void {
    this.#initTagCodingShortcutKeys()
    this.#selectTagAndCodingChanges()
    this.#activateInitialTab()
    this.#notifyCodingFormValid()
    this.#notifyTagFormValid()
    this.#loadLazyLoadComponents()
    this.#selectTagAddOrUpdateResponses()
    this.#selectCodingAddOrUpdateResponses()
    this.#notifySwitchToTagViewAfterSuccess()
    this.#selectTagCodingShortcutKeysAction()
  }

  #loadLazyLoadComponents(): void {
    this.tagRuleInfoComp = import('@venio/feature/tag-email-thread').then(
      ({ TagRuleInfoContentComponent }) => TagRuleInfoContentComponent
    )
  }

  #selectIsCodingDataValid(): void {
    this.documentCodingFacade.selectIsCodingDataValid$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isCodingDataValid) => {
        this.isCodingDataValid.set(isCodingDataValid)
      })
  }

  #selectTagCodingResponses(): void {
    combineLatest([
      this.documentTagFacade.applyDocumentTagSuccessResponse$,
      this.documentTagFacade.applyDocumentTagErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return

        if (
          success?.data?.showTagCodingConfirmation &&
          (success?.data?.tagResult?.length > 0 ||
            success.data?.codingResult?.updatedCodingList?.length > 0)
        )
          this.openTagCodingSummaryDialog(success.data)

        this.#showMessage(success ? success.message : error.message, {
          style: success ? 'success' : 'error',
        })
        this.isSavingData.set(false)

        if (success) {
          this.#notifyTagCodingDataUpdateToPopupWindow(success)
          this.#resetDocumentTagSuccessResponseState()
          this.#closeDialog()
        } else {
          this.#resetDocumentTagErrorResponseState()
        }
        this.changeDetectorRef.markForCheck()
      })
  }

  public openTagCodingSummaryDialog(tagCodingSummary: any): void {
    import(
      '../../tag-coding/tag-coding-summary-dialog/tag-coding-summary-dialog.component'
    ).then((td) => {
      const dialogRef = this.dialogService.open({
        content: td.TagCodingSummaryDialogComponent,
        maxWidth: '1600',
        minWidth: '250',
        width: '80%',
        height: '90vh',
      })

      const tagCodingSummaryDialog = dialogRef.content.instance
      tagCodingSummaryDialog.tagCodingDetails = tagCodingSummary
      tagCodingSummaryDialog.isBulkTagCoding = true
    })
  }

  #showTagCodingConfirmationMessage(): void {
    const message = this.isTagAndCodingFieldUpdate()
      ? 'Are you sure you want to undo? All the changes will be reverted back to the previous values.'
      : 'Saved actions will not be reverted back'

    this.confirmationDialogService
      .showDialogByType(this.isTagAndCodingFieldUpdate(), 'Undo', message)
      .pipe(
        filter((confirmed) => confirmed),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.reviewPanelFacade.setShortcutKeysAction =
          ShortcutKeyActions.UNDO_TAG_CODING
      })
  }

  private tagCodingShortcutHandler: Callback = (event, combo): void => {
    const actions: { [key: string]: () => void } = {
      [ShortcutKeyBindings.UNDO_TAG_CODING]: () => {
        this.#showTagCodingConfirmationMessage()
      },
    }

    // Check if the combo is one of the defined shortcuts
    if (actions[combo]) {
      event.preventDefault()
      event.stopPropagation()
      actions[combo]() // Execute the corresponding action
    }
  }

  #initTagCodingShortcutKeys(): void {
    this.shortcutManager = new ShortcutManager()
    this.shortcutManager.bind(
      [ShortcutKeyBindings.UNDO_TAG_CODING],
      this.tagCodingShortcutHandler
    )
  }

  #removeTagCodingShortcutKeys(): void {
    this.shortcutManager.unbind(
      [ShortcutKeyBindings.UNDO_TAG_CODING],
      undefined,
      this.tagCodingShortcutHandler
    )
  }

  #selectTagCodingShortcutKeysAction(): void {
    this.reviewPanelFacade.getShortcutKeysAction
      .pipe(
        filter((action) => action === ShortcutKeyActions.SAVE_TAG_CODING),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.saveTag()
      })
  }

  #activateInitialTab(): void {
    setTimeout(() => {
      this.changeDetectorRef.markForCheck()
      this.mainTabStrip.selectTab(0)
    })
  }

  #selectIsBulkCodingLoaded(): void {
    this.documentCodingFacade.isDocumentCodeLoading$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((loading) => {
        this.isDocumentCodingComponentLoaded.set(!loading)
      })
  }

  #getSelectedDocuments(): void {
    this.documentsFacade.getSelectedDocuments$
      .pipe(
        debounceTime(50),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((selectedDocuments) => {
        if (selectedDocuments && selectedDocuments.length === 0) {
          this.formMessage =
            'Please select a document to apply tags and coding.'

          this.isTagAndCodingVisible = false
        } else {
          this.isTagAndCodingVisible = true
        }

        this.changeDetectorRef.markForCheck()
      })
  }

  #closeDialog(): void {
    this.#resetMultiCodingValue()
    this.#resetDocumentProjectTags()
    this.dialogRef.close()
  }

  #resetDocumentTagSuccessResponseState(): void {
    this.documentTagFacade.resetDocumentTagState([
      'applyDocumentTagSuccessResponse',
      'isTagDataModified',
    ])
  }

  #resetDocumentTagErrorResponseState(): void {
    this.documentTagFacade.resetDocumentTagState([
      'applyDocumentTagErrorResponse',
    ])
  }

  #resetMultiCodingValue(): void {
    this.documentCodingFacade.resetDocumentCodingState([
      'updatedCodingFieldInfoIds',
    ])
  }

  #fetchDocumentTagCommentValidation(): void {
    this.documentTagFacade.areTagCommentsMissing$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isTagCommentValidationRequired) => {
        this.isTagCommentValidationRequired = isTagCommentValidationRequired
      })
  }

  #showMessage(content = '', type: KendoNotificationType): void {
    if (!content?.trim()) return

    this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })
  }

  public toggleCodingFieldForm(): void {
    this.changeDetectorRef.markForCheck()
    this.isCodingFieldAddUpdate.set(!this.isCodingFieldAddUpdate())
    this.#setDialogTitle()
  }

  #setDialogTitle(): void {
    this.dialogTitle = this.isCodingFieldAddUpdate()
      ? 'Add Coding'
      : this.defaultTitle
  }

  #notifyCodingFormValid(): void {
    this.isCodingFormValid
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((isValid) => {
        this.isValidCodingForm = isValid
        this.changeDetectorRef.markForCheck()
      })
  }

  #selectTagAndCodingChanges(): void {
    combineLatest([
      this.documentTagFacade.selectIsTagDataModified$,
      this.documentCodingFacade.selectIsCodingDataModified$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(([tag, coding]) => {
        const isDataModified = Boolean(tag || coding)
        this.isTagAndCodingFieldUpdate.set(isDataModified)
      })
  }

  #selectCodingAddOrUpdateResponses(): void {
    combineLatest([
      this.codingFacade.selectAddOrUpdateCodingFieldSuccessResponse$,
      this.codingFacade.selectAddOrUpdateCodingFieldErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success || error)),
        // The debounced time is required here to ensure,
        // the form state completes its action and properly shows the success message.
        // Otherwise,
        // the bulk code component will be loaded before the form state completes its action
        // and the success message will not be shown.
        debounceTime(500),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return

        this.isSavingData.set(false)

        if (error) return
        //Switch To bulk code after form Success
        this.#notifyTagCodingDataUpdateToPopupWindow(success)
        this.toggleCodingFieldForm()
      })
  }

  #selectTagAddOrUpdateResponses(): void {
    this.tagsFacade.selectTagAddOrUpdateSuccessResponse$
      .pipe(
        filter((success) => Boolean(success)),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        this.#notifyTagCodingDataUpdateToPopupWindow(success)
      })
  }

  #notifySwitchToTagViewAfterSuccess(): void {
    this.tagsFacade.selectSwitchToTagViewNotified$
      .pipe(
        debounceTime(500),
        filter((isSwitchToTagView) => Boolean(isSwitchToTagView)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isSwitchToTagView) => {
        this.#resetDocumentProjectTags()
        this.isSavingData.set(false)
        this.#fetchTagTree()
        this.tagsFacade.notifyToSwitchTagView(false)
        //Switch To bulk tag after form Success
        this.toggleTagFieldForm()
      })
  }

  #notifyTagFormValid(): void {
    this.tagsFacade.selectTagFormGroupValid$
      .pipe(
        debounceTime(100),
        filter((isValid) => Boolean(isValid)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isValid) => {
        this.isTagFormValid.set(isValid)
      })
  }

  public toggleTagFieldForm(): void {
    this.changeDetectorRef.markForCheck()
    this.isTagAddUpdate.set(!this.isTagAddUpdate())
    this.#setTagDialogTitle()
  }

  public setFlagForTagAddUpdate(): void {
    this.changeDetectorRef.markForCheck()
    this.isTagFormValid.set(false)
    this.isTagAddUpdate.set(true)
    this.#setTagDialogTitle()
  }

  public navigateTagForm(): void {
    this.isTagAddUpdate.set(false)
    this.dialogTitle = 'Edit - ' + this.defaultTitle
  }

  #setTagDialogTitle(): void {
    this.dialogTitle = this.isTagAddUpdate() ? 'Add Tags' : this.defaultTitle
  }

  #initReviewSetId(): void {
    this.reviewSetId = this.reviewSetState.isBatchReview()
      ? this.reviewSetState.reviewSetId()
      : 0
  }

  #fetchTagTree(): void {
    this.tagsFacade.fetchTagTree(this.projectId, this.reviewSetId)
  }

  public updateDialogTitle(tab: SelectEvent): void {
    this.dialogTitle = this.defaultTitle
    this.isTagTab.set(tab.index === 0)
    this.isCodingTab.set(tab.index !== 0)
  }

  public ngOnDestroy(): void {
    this.upateIsBulkDocument(false)
    this.#removeTagCodingShortcutKeys()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public upateIsBulkDocument(value: boolean): void {
    this.documentsFacade.updateIsBulkDocument(value)
  }

  public close(status: string): void {
    this.opened = false
    this.#closeDialog()
  }

  public openDialog(): void {
    this.opened = true
  }

  public saveTag(): void {
    this.isSavingData.set(true)
    if (this.isCodingFieldAddUpdate() && this.isValidCodingForm) {
      this.#saveCodingForm() // This is the method that is being called Saving Coding Form
      return
    }
    if (this.isTagAddUpdate() && this.isTagFormValid()) {
      this.#saveTagForm() // This is the method that is being called Saving Tag Form
      return
    }

    if (this.isTagCommentValidationRequired) {
      this.#showMessage(this.tagCommentValidationMessage, {
        style: 'error',
      })
      this.isSavingData.set(false)
      return
    }
    //This is the method that is being called Bulk Tag / Coding
    this.isSavingData.set(this.isTagAndCodingFieldUpdate())
    if (this.isTagAndCodingFieldUpdate()) {
      this.#saveBulkTagCoding()
    }
  }

  #saveBulkTagCoding(): void {
    this.documentTagUtilityService.saveTag(
      this.projectId,
      PageControlActionType.SAVE
    )
  }

  #saveTagForm(): void {
    this.tagsFacade.notifyIsAddOrUpdateTagField(true)
  }

  #saveCodingForm(): void {
    this.codingFacade.notifyIsAddOrUpdateCodingField(true)
  }

  public showAllTagRules(): void {
    this.documentTagFacade.showHideTagRuleHeader(false)
    this.#resetDocumentTagRuleState()
  }

  #notifyTagCodingDataUpdateToPopupWindow(response: ResponseModel): void {
    const popoutWindow: Window = this.windowManager.getWindow(
      AppIdentitiesTypes.REVIEW_PANEL
    )
    let isTagUpdate = this.isTagAddUpdate()
    let isCodingUpdate = this.isCodingFieldAddUpdate()

    if (!this.isTagAddUpdate() && response)
      isTagUpdate = response.data?.tagResult?.length > 0

    if (!this.isCodingFieldAddUpdate())
      isCodingUpdate = !response.data?.codingResult?.hasError

    if (popoutWindow) {
      this.messengerService.sendMessage(
        {
          payload: {
            type: WindowMessageType.DATA_UPDATE,
            content: {
              componentId: UtilityPanelType.TAG_CODING,
              projectId: this.projectId,
              isTagupdate: isTagUpdate,
              isCodingUpdate: isCodingUpdate,
            },
          },
        },
        popoutWindow
      )
    }
  }

  #resetDocumentTagRuleState(): void {
    this.documentTagFacade.resetDocumentTagState([
      'filterTagRuleId',
      'tagRuleViolationList',
      'showTagRuleList',
    ])
  }

  #resetDocumentProjectTags(): void {
    this.documentTagFacade.resetDocumentTagState([
      'projectTags',
      'documentTags',
    ])
  }
}
