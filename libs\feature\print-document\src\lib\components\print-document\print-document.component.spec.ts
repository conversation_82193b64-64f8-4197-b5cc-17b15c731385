import { ComponentFixture, TestBed } from '@angular/core/testing'
import { PrintDocumentComponent } from './print-document.component'
import { provideMockStore } from '@ngrx/store/testing'
import { DatePipe } from '@angular/common'
import { LabelModule } from '@progress/kendo-angular-label'
import { InputsModule, TextBoxModule } from '@progress/kendo-angular-inputs'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { PopoverModule } from '@progress/kendo-angular-tooltip'
import { ReactiveFormsModule } from '@angular/forms'
import {
  DocumentsFacade,
  FieldFacade,
  PrintDocumentFacade,
  PrintDocumentService,
  PrintImageActionTemplateService,
  SearchFacade,
} from '@venio/data-access/review'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'
import { ExpansionPanelModule } from '@progress/kendo-angular-layout'
import { provideAnimations } from '@angular/platform-browser/animations'

describe('PrintDocumentComponent', () => {
  let component: PrintDocumentComponent
  let fixture: ComponentFixture<PrintDocumentComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PrintDocumentComponent],
      imports: [
        InputsModule,
        TextBoxModule,
        LabelModule,
        ButtonModule,
        PopoverModule,
        ReactiveFormsModule,
        SvgLoaderDirective,
        ExpansionPanelModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        DocumentsFacade,
        PrintDocumentFacade,
        SearchFacade,
        FieldFacade,
        PrintDocumentService,
        PrintDocumentFormService,
        DatePipe,
        provideMockStore({}),
        PrintImageActionTemplateService,
        provideAnimations(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PrintDocumentComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
