@import '@fontsource/roboto';
@import "@fontsource/roboto/100.css";
@import "@fontsource/roboto/300.css";
@import "@fontsource/roboto/500.css";
@import "@fontsource/roboto/700.css";
@import "@fontsource/roboto/900.css";

// TO AVOID: some kendo components which were overridden by tailwind base
// e.g. kendo dropdownlist, kendo editor
@tailwind base;

//kendo theme generated by theme builder
@import './theme/kendo/rel-overrides/index';
// custom overrides
// e.g. _buttons, _checkbox
@import './theme/kendo/rel-overrides/components/drawer';
@import './theme/kendo/rel-overrides/components/list';
@import './theme/kendo/rel-overrides/components/menu';
@import './theme/kendo/rel-overrides/components/button';
@import './theme/kendo/rel-overrides/components/notification';
@import './theme/kendo/rel-overrides/components/grid';
@import './theme/kendo/rel-overrides/components/dialog';
@import './theme/kendo/rel-overrides/components/input';
@import './theme/kendo/rel-overrides/components/tabstrip';
@import './theme/kendo/rel-overrides/components/popover';
@import './theme/kendo/rel-overrides/components/treelist';
@import './theme/kendo/rel-overrides/components/treeview';
@import './theme/kendo/rel-overrides/components/listbox';
@import './theme/kendo/rel-overrides/components/layout';
@import './theme/kendo/rel-overrides/components/progress';
@import './theme/kendo/rel-overrides/components/editor';
@import './theme/kendo/rel-overrides/components/graph';

// Tailwind utility classes

@tailwind components;
@tailwind utilities;

// tailwind override of font to make roboto default.
@layer base {
  * {
    font-family: 'Roboto', system-ui;
  }
}

body,
html {
  height: 100%;
}
:focus-visible {
  outline: none;
}
