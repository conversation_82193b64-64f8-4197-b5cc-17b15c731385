import {
  ChangeDetectionStrategy,
  Component,
  TemplateRef,
  Type,
  ViewChild,
  OnInit,
  OnDestroy,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { combineLatest, filter, Subject, takeUntil } from 'rxjs'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import {
  DocumentsFacade,
  InitialSearchResultParameter,
  SearchFacade,
} from '@venio/data-access/review'
import { RsmfCreationDialogComponent } from '../rsmf-creation-dialog/rsmf-creation-dialog.component'
import { VenioNotificationService } from '@venio/feature/notification'

@Component({
  selector: 'venio-rsmf-creation-container',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './rsmf-creation-container.component.html',
  styleUrl: './rsmf-creation-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RsmfCreationContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  @ViewChild('dialogContent', { static: true })
  private readonly dialogContent: TemplateRef<any>

  constructor(
    private documentsFacade: DocumentsFacade,
    private dialogService: DialogService,
    private searchFacade: SearchFacade,
    private notification: VenioNotificationService
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    combineLatest([
      this.documentsFacade.selectDocumentMenuEvent$,
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getSearchInitialParameters$,
    ])
      .pipe(
        filter(([event]) => event === DocumentMenuType.GENERATE_RSMF),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([_, isBatchSelected, selectedDocs, unselectedDocs, searchParams]) => {
          let selectedDocuments = 0
          selectedDocuments = this.getSelectedDocumentCount(
            isBatchSelected,
            selectedDocuments,
            searchParams,
            unselectedDocs,
            selectedDocs
          )

          if (selectedDocuments === 0) {
            this.notification.showError(
              'Please select at least one document for conversion.'
            )
            this.#resetMenuLoadingState()
            this.#resetMenuEventState()
          } else {
            // launch the dialog
            this.#handleLazyLoadedDialog()
          }
        }
      )
  }

  private getSelectedDocumentCount(
    isBatchSelected: boolean,
    selectedDocuments: number,
    searchParams: InitialSearchResultParameter,
    unselectedDocs: number[],
    selectedDocs: number[]
  ): number {
    if (isBatchSelected) {
      selectedDocuments = searchParams.totalHitCount - unselectedDocs.length
    } else {
      selectedDocuments = selectedDocs.length
    }
    return selectedDocuments
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import('../rsmf-creation-dialog/rsmf-creation-dialog.component').then(
      (d) => {
        // reset the loading indicator
        this.#resetMenuLoadingState()

        // launch the dialog
        this.#launchDialogContent(d.RsmfCreationDialogComponent)

        // once the dialogRef instance is created
        this.#handleEditDialogCloseEvent()
      }
    )
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      maxHeight: '65vh',
      maxWidth: '70vw',
      minHeight: '65vh',
      minWidth: '70vw',
    })

    const componentRef = this.dialogRef.content
      .instance as RsmfCreationDialogComponent
    componentRef.dialogRef = this.dialogRef
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
