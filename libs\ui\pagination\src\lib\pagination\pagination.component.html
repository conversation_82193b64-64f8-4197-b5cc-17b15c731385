<div
  class="t-flex t-flex-row-reverse t-items-center t-text-sm t-gap-[0.5rem] t-text-[#595959] t-font-medium"
  kendoTooltip>
  <button
    kendoButton
    #last
    class="!t-p-[0_0.5rem] !t-px-[0.7rem] t-h-[1.4rem] hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
    (click)="goTo(Page.Last)"
    rounded="small"
    fillMode="outline"
    title="Last Page"
    size="none"
    [disabled]="
      totalRecords === 0 || currentPage === totalPages || totalPages <= 1
    ">
    <span
      venioSvgLoader
      svgUrl="assets/svg/icon-last-page.svg"
      hoverColor="#FFFFFF"
      width="0.8rem"
      height="0.8rem"
      [parentElement]="last.element">
      <kendo-loader size="small" />
    </span>
  </button>

  <button
    kendoButton
    #next
    class="!t-p-[0_0.5rem] !t-px-[0.7rem] t-h-[1.4rem] hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
    (click)="goTo(Page.Next)"
    rounded="small"
    fillMode="outline"
    title="Next Page"
    size="none"
    [disabled]="
      totalRecords === 0 || currentPage === totalPages || totalPages <= 1
    ">
    <span
      venioSvgLoader
      svgUrl="assets/svg/icon-navigate-next.svg"
      hoverColor="#FFFFFF"
      width="0.8rem"
      height="0.8rem"
      [parentElement]="next.element">
      <kendo-loader size="small" />
    </span>
  </button>

  <kendo-numerictextbox
    *ngIf="showPageJumper"
    class="t-w-[1rem] !t-p-[0_0.5rem] t-h-[1.4rem]"
    [min]="1"
    [max]="totalPages"
    format="##"
    [autoCorrect]="true"
    [spinners]="false"
    (valueChange)="onChange($event)"
    name="currentPage"
    [(ngModel)]="currentPage"
    size="none"
    [disabled]="totalRecords === 0" />

  <button
    kendoButton
    #prev
    class="!t-p-[0_0.5rem] !t-px-[0.7rem] t-h-[1.4rem] hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
    (click)="goTo(Page.Previous)"
    rounded="small"
    fillMode="outline"
    title="Previous Page"
    size="none"
    [disabled]="totalRecords === 0 || currentPage === 1 || totalPages <= 1">
    <span
      venioSvgLoader
      svgUrl="assets/svg/icon-navigate-prev.svg"
      hoverColor="#FFFFFF"
      width="0.8rem"
      height="0.8rem"
      [parentElement]="prev.element">
      <kendo-loader size="small" />
    </span>
  </button>

  <button
    kendoButton
    #first
    class="!t-p-[0_0.5rem] !t-px-[0.7rem] t-h-[1.4rem] hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
    (click)="goTo(Page.First)"
    rounded="small"
    fillMode="outline"
    title="First Page"
    size="none"
    [disabled]="totalRecords === 0 || currentPage === 1 || totalPages <= 1">
    <span
      venioSvgLoader
      svgUrl="assets/svg/icon-first-page.svg"
      hoverColor="#FFFFFF"
      width="0.8rem"
      height="0.8rem"
      [parentElement]="first.element">
      <kendo-loader size="small"></kendo-loader>
    </span>
  </button>

  <div class="t-flex t-items-center t-justify-end" *ngIf="showPageSize">
    <kendo-label class="k-form t-mx-1 t-flex t-items-center t-text-center">
      <kendo-numerictextbox
        format="##"
        [spinners]="false"
        [autoCorrect]="true"
        [style.width.px]="inputWidth()"
        class="!t-border-[1px] !t-text-center !t-border-[#C6B3FF] !t-bg-[#DDD1FF] !t-text-[#ffffff] v-purple-input"
        *ngIf="showRowNumberInputBox"
        [(ngModel)]="firstDocumentOfCurrentPage"
        (keydown)="onKeyDown($event)"
        [readonly]="false"
        [disabled]="totalRecords === 0" />
      <span *ngIf="showRowNumberInputBox" #mirror class="v-input-mirror">{{
        firstDocumentOfCurrentPage || 0
      }}</span>
      <span *ngIf="!showRowNumberInputBox">
        {{ firstDocumentOfCurrentPage }}
      </span>
    </kendo-label>
    <span>
      -
      {{
        (pageSize * currentPage > totalRecords
          ? totalRecords
          : pageSize * currentPage) || 0
      }}
      Of {{ totalRecords || 0 }}
    </span>
    <kendo-dropdownlist
      class="!t-w-[5rem] !t-border-[#707070] t-mx-2"
      [data]="sizes"
      [value]="pageSize"
      (valueChange)="onPageSizeChange($event)"
      [disabled]="totalRecords === 0" />
    per page
  </div>
</div>
