<kendo-dialog-titlebar (close)="close('cancel')">
  <div class="t-flex t-justify-between t-w-full t-items-center">
    <div class="t-w-[14%] t-px-2">
      <div class="t-block">{{ dialogTitle }}</div>
    </div>
    <div class="t-w-[72%] t-t-px-2">
      <div class="t-block">
        <div
          #appendNotification
          class="v-append-notification-container t-text-sm t-inline-block t-text-xs t-overflow-hidden t-relative t-h-14 t-w-full"></div>
      </div>
    </div>
    <div class="t-w-[14%] t-px-2"></div>
  </div>
</kendo-dialog-titlebar>

<kendo-tabstrip (tabSelect)="onSelect($event)">
  <kendo-tabstrip-tab
    title="Create PST"
    *ngIf="showCreatePSTTab"
    [selected]="isSelected(0)">
    <ng-template kendoTabContent>
      <div class="t-flex t-flex-col t-w-full t-mt-3">
        <kendo-grid
          class="t-flex t-flex-col-reverse v-custom-grid-table"
          venioDynamicHeight
          [isKendoDialog]="true"
          [data]="gridDataPST.data"
          [loading]="isGettingFilesForResponsivePst | async"
          [pageSize]="pageSize"
          [skip]="skip"
          [pageable]="true"
          [resizable]="true">
          <ng-template kendoPagerTemplate>
            <div
              class="t-flex t-text-xs t-pl-5"
              *ngIf="tabStatus === 0 && showWarningMessage">
              <div class="t-flex t-flex-col t-gap-1">
                <p>
                  Click
                  <a
                    class="text-primary t-font-semibold t-cursor-pointer"
                    (click)="getErrorFiles()">
                    Here
                  </a>
                  to download excluded files.
                </p>
              </div>
            </div>

            <div *ngIf="tabStatus === 0 && showWarningMessage"></div>
            <div class="t-flex t-w-full t-justify-end">
              <venio-pagination
                [currentPage]="currentPage"
                [disabled]="gridDataPST?.data?.length === 0"
                [totalRecords]="gridDataPST?.total"
                [pageSize]="pageSize"
                [showPageJumper]="false"
                [showPageSize]="true"
                [showRowNumberInputBox]="true"
                (pageChanged)="pageChanged($event)"
                (pageSizeChanged)="pageSizeChanged($event)"
                class="t-px-5 t-block t-py-2">
              </venio-pagination>
            </div>
          </ng-template>
          <kendo-grid-column
            field="fileId"
            title="#"
            headerClass="t-text-primary"
            [width]="75"
            [minResizableWidth]="30"></kendo-grid-column>
          <kendo-grid-column
            field="rootFolder"
            title="Root Folder"
            headerClass="t-text-primary"
            [width]="160"
            [minResizableWidth]="130">
            <ng-template kendoGridHeaderTemplate>
              <span title="Root Folder">Root Folder</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <span title="{{ dataItem.rootFolder }}">{{
                dataItem.rootFolder
              }}</span>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column
            field="logicalPath"
            headerClass="t-text-primary"
            title="Logical Path">
            <ng-template kendoGridHeaderTemplate>
              <span title="Logical Path">Logical Path</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <span title="{{ dataItem.logicalPath }}">{{
                dataItem.logicalPath
              }}</span>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>
      </div>
    </ng-template>
  </kendo-tabstrip-tab>
  <kendo-tabstrip-tab title="PST Status" [selected]="isSelected(1)">
    <ng-template kendoTabContent>
      <kendo-grid
        class="t-flex t-flex-col-reverse v-custom-grid-table"
        venioDynamicHeight
        [isKendoDialog]="true"
        [data]="gridDataStatus.data"
        [loading]="isDeletingPst | async"
        [pageSize]="pageSizeStatus"
        [skip]="skipStatus"
        [pageable]="true"
        [resizable]="true">
        <ng-template kendoPagerTemplate>
          <div
            class="t-flex t-w-full t-justify-end t-items-center"
            kendoTooltip>
            <button
              #parentRefresh
              class="!t-p-1.5 t-h-fit !t-border-[#9AD3A6] t-leading-[0.8] hover:!t-bg-[#9AD3A6]"
              kendoButton
              size="none"
              fillMode="outline"
              (click)="onRefreshClick()"
              title="Refresh">
              <span
                [parentElement]="parentRefresh.element"
                venioSvgLoader
                [svgUrl]="refreshSvgUrl"
                color="#9AD3A6"
                hoverColor="#FFFFFF"
                height="1rem"
                width="1rem"></span>
            </button>
            <kendo-grid-spacer></kendo-grid-spacer>
            <venio-pagination
              [currentPage]="currentPageStatus"
              [disabled]="gridDataStatus?.data?.length === 0"
              [totalRecords]="gridDataStatus?.total"
              [pageSize]="pageSizeStatus"
              [showPageJumper]="false"
              [showPageSize]="true"
              [showRowNumberInputBox]="true"
              (pageChanged)="pageChanged($event)"
              (pageSizeChanged)="pageSizeChanged($event)"
              class="t-px-5 t-block t-py-2">
            </venio-pagination>
          </div>
        </ng-template>
        <kendo-grid-column
          field="jobId"
          title="#"
          headerClass="t-text-primary"
          [width]="40"
          [minResizableWidth]="40"></kendo-grid-column>
        <kendo-grid-column
          field="pstName"
          title="PST Name"
          headerClass="t-text-primary"
          [width]="150"
          [minResizableWidth]="150">
          <ng-template kendoGridHeaderTemplate>
            <span kendoTooltip title="PST Name">PST Name</span>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <span kendoTooltip title="{{ dataItem.pstFileName }}">{{
              dataItem.pstFileName
            }}</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column
          field="createdByOn"
          title="Created By & On"
          headerClass="t-text-primary"
          [width]="150"
          [minResizableWidth]="150">
          <ng-template kendoGridHeaderTemplate>
            <span kendoTooltip title="Created By & On">Created By & On</span>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <span
              kendoTooltip
              title="{{ dataItem.createdByUser }} {{ dataItem.createdOn }}">
              <span class="t-font-semibold">{{
                dataItem.createdByUser | titlecase
              }}</span>
              <span> {{ dataItem.createdOn | date : 'dd-MM-yyyy' }}</span>
              <span class="t-text-xs">
                {{ dataItem.createdOn | date : 'shortTime' }}</span
              >
            </span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column
          field="status"
          title="Status"
          headerClass="t-text-primary"
          [width]="250"
          [minResizableWidth]="250">
          <ng-template kendoGridHeaderTemplate>
            <span kendoTooltip title="Status">Status</span>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <span kendoTooltip title="{{ dataItem.status }}">{{
              dataItem.status
            }}</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column
          field="action"
          title="Action"
          headerClass="!t-text-primary"
          [width]="150"
          [minResizableWidth]="150">
          <ng-template kendoGridHeaderTemplate>
            <span kendoTooltip title="Action">Action</span>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <kendo-buttongroup>
              @for (icon of svgIconForGridControls; track icon) {
              <button
                kendoButton
                #actionGrid
                *ngIf="
                  icon.actionType !== 'Download Excluded Files' ||
                  dataItem.hasExcludedFilesLog
                "
                class="t-p-1 t-w-[2.56rem] t-h-[2.125rem] t-border t-border-[#9BD2A7] t-rounded-[2px] t-opacity-100 hover:!t-bg-[#9AD3A6]"
                (click)="browseActionClicked(icon.actionType, dataItem)"
                fillMode="outline"
                kendoTooltip
                [title]="icon.actionType"
                [ngClass]="{
                  'hover:!t-border-[#1EBADC] hover:!t-bg-[#1EBADC]':
                    icon.actionType !== 'Delete',
                  'hover:!t-border-[#ED7425] hover:!t-bg-[#ED7425]':
                    icon.actionType === 'Delete',
                }"
                size="none">
                <span
                  [parentElement]="actionGrid.element"
                  venioSvgLoader
                  hoverColor="#FFFFFF"
                  [color]="'#979797'"
                  [svgUrl]="icon.iconPath"
                  height="0.9rem"
                  width="1rem"></span>
              </button>
              }
            </kendo-buttongroup>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </ng-template>
  </kendo-tabstrip-tab>
</kendo-tabstrip>

<kendo-dialog-actions>
  <div class="t-flex t-text-xs" *ngIf="tabStatus === 0 && showWarningMessage">
    <div class="t-block t-text-error t-pr-2">Note:</div>
    <div class="t-flex t-flex-col t-gap-1">
      <p>
        {{ warningMessage }}
      </p>
    </div>
  </div>
  <div class="t-flex t-gap-4 t-justify-end">
    @if(this.tabStatus === 1 || isSelected(1)){
    <button
      kendoButton
      (click)="close('yes')"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CLOSE
    </button>
    } @else {

    <div class="t-flex t-items-center">
      <div class="t-flex t-relative t-top-[1px]">
        <input
          type="checkbox"
          [(ngModel)]="generateSeparatePstPerCustodian"
          id="SplitPstPerCustodian"
          kendoCheckBox />
      </div>
      <div class="t-px-2 t-content-center t-relative t-top-[1px]">
        <kendo-label
          for="SplitPstPerCustodian"
          text="Generate Separate PST per Custodian">
        </kendo-label>
      </div>
    </div>
    <button
      kendoButton
      [disabled]="isResponsivePstCreating | async"
      (click)="createPST()"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save-button">
      CREATE
    </button>
    <button
      kendoButton
      (click)="close('yes')"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CANCEL
    </button>
    }
  </div>
</kendo-dialog-actions>
