<div class="t-flex t-p-[0.44rem] t-relative" #toolbarContainer kendoTooltip>
  <button
    kendoButton
    #parentDownload
    *venioHasUserGroupRights="UserRights.ALLOW_TO_DOWNLOAD_NATIVE_FILES"
    class="!t-p-[0.3rem]"
    fillMode="clear"
    size="none"
    title="Download"
    (click)="onActionClick('download')">
    <span
      [parentElement]="parentDownload.element"
      venioSvgLoader
      hoverColor="#FFBB12"
      color="#979797"
      [svgUrl]="'assets/svg/icon-preview-download.svg'"
      height="1.3rem"
      width="1.3rem"></span>
  </button>

  <button
    kendoButton
    #parentRefresh
    class="!t-p-[0.3rem]"
    fillMode="clear"
    size="none"
    title="Refresh"
    (click)="onActionClick('refresh')">
    <span
      [parentElement]="parentRefresh.element"
      venioSvgLoader
      hoverColor="#FFBB12"
      [applyEffectsTo]="'both'"
      [svgUrl]="'assets/svg/icon-preview-refresh.svg'"
      height="1rem"
      width="1.3rem"></span>
  </button>

  <button
    kendoButton
    #parentZoomIn
    class="!t-p-[0.3rem]"
    fillMode="clear"
    size="none"
    title="Zoom In"
    (click)="onActionClick('zoom_in')">
    <span
      [parentElement]="parentZoomIn.element"
      venioSvgLoader
      hoverColor="#FFBB12"
      [applyEffectsTo]="'fill'"
      [svgUrl]="'assets/svg/icon-pdf-utilities-zoomin.svg'"
      height="1.3rem"
      width="1.3rem"></span>
  </button>

  <button
    kendoButton
    #parentZoomOut
    class="!t-p-[0.3rem]"
    fillMode="clear"
    size="none"
    title="Zoom Out"
    (click)="onActionClick('zoom_out')">
    <span
      [parentElement]="parentZoomOut.element"
      venioSvgLoader
      hoverColor="#FFBB12"
      [applyEffectsTo]="'fill'"
      [svgUrl]="'assets/svg/icon-pdf-utilities-zoomout.svg'"
      height="1.3rem"
      width="1.3rem"></span>
  </button>
</div>
<div
  class="t-flex t-flex-col t-border t-border-l-0 t-border-r-0 t-border-b-0 t-border-t-1 t-border-[#dbdbdb] t-p-0"
  [ngStyle]="{
    height: 'calc(100% - ' + (toolbarContainer.offsetHeight + 32) + 'px)',
    transform: 'scale(' + nearNativeState.scaleFactor() + ')',
    transformOrigin: 'top left'
  }">
  <ng-container *ngComponentOutlet="viewer | async; inputs: inputs" />
</div>
