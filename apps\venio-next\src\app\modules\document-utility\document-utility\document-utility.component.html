<div class="t-flex t-flex-col" *ngIf="loadComponent()">
  <!-- for heder ui-->
  <!-- <ng-container
      [ngComponentOutlet]="
        documentUtilityPaginationComponent | async
      "></ng-container> -->
  <div
    *ngIf="isReviewPanelPopoutLoding()"
    class="k-i-loading t-absolute t-h-full t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-top-0 t-left-0 t-right-0 t-bottom-0 t-text-[58px] t-z-10"></div>

  <div class="t-flex t-p-5">
    <div class="t-flex t-w-1/3 t-gap-3">
      <button
        kendoButton
        *ngIf="!isReviewPanelPopout"
        [svgIcon]="chevronLeftIcon"
        fillMode="outline"
        look="clear"
        class="t-capitalize !t-border-[#ccc] t-gap-0.5 t-px-2 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF] hover:!t-text-[#FFFFFF]"
        (click)="exitViewer()">
        Exit Viewer
      </button>
      <div class="t-flex t-gap-3" *ngIf="reviewSetState.isBatchReview()">
        <button
          kendoButton
          fillMode="outline"
          class="t-px-1.5 t-py-0.5 !t-border-[#ccc]"
          (click)="onMarkAsReviewed()"
          kendoTooltip
          [title]="reviewedTooltip()">
          <span
            venioSvgLoader
            height="22px"
            width="23px"
            [svgUrl]="isReviewedIcon()"></span>
        </button>
        <button
          kendoButton
          fillMode="outline"
          class="t-px-1.5 t-py-0.5 !t-border-[#ccc]"
          (click)="checkInBatch()"
          kendoTooltip
          title="Batch Check In">
          <span
            venioSvgLoader
            height="22px"
            width="23px"
            [svgUrl]="batchCheckInIcon()"></span>
        </button>
      </div>
    </div>
    <div
      class="t-w-1/3 t-gap-2 t-flex t-justify-center"
      *ngIf="!reviewSetState.isBatchReview()">
      <ng-container
        *ngComponentOutlet="
          projectInfoComp | async;
          injector: projectInfoInjector
        "></ng-container>
    </div>
    <div
      class="t-w-1/3 t-gap-2 t-flex t-justify-center"
      *ngIf="
        reviewSetState.isBatchReview() && reviewSetState.reviewSetBasicInfo()
      ">
      <div class="t-flex t-gap-2 t-items-center">
        <span class="t-font-medium t-text-[#979797]"
          >Review set name -
          {{ reviewSetState.reviewSetBasicInfo()?.reviewSetName }}</span
        >
      </div>
    </div>
    <div
      class="t-flex t-flex-auto t-w-1/3 t-items-end t-justify-end t-pr-2"
      kendoTooltip>
      <div class="t-flex t-self-center">
        <venio-shortcut-key-dictionary
          [type]="'review'"
          [showKeyboardShortcuts]="true">
        </venio-shortcut-key-dictionary>
      </div>
      <div class="t-flex t-self-center">
        <venio-layout-menu></venio-layout-menu>
      </div>
      <div class="t-flex t-gap-2 t-items-center">
        <ng-container
          *ngFor="let icon of svgIconForPageControls; let i = index">
          <button
            kendoButton
            class="!t-p-[0.3rem] hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
            fillMode="outline"
            size="none"
            #parentElTag
            (click)="documentNavigation(icon.actionType)"
            [title]="icon.actionText">
            <span
              [parentElement]="parentElTag.element"
              venioSvgLoader
              hoverColor="#FFFFFF"
              [svgUrl]="icon.iconPath"
              height="0.8rem"
              width="1rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>

          <!-- Conditionally render the input field after the first two elements -->
          <div *ngIf="i === 1">
            <div class="t-flex t-items-center">
              <div class="t-flex-1">
                @if(isDocumentExistsInSearchScope){
                <kendo-textbox
                  class="!t-w-[3rem] t-h-[33.6px] !t-border-[1px] !t-border-[#C6B3FF] !t-bg-[#DDD1FF] !t-text-[#ffffff] v-purple-input"
                  size="large"
                  [formControl]="seqNoCtrl">
                </kendo-textbox>
                } @else{
                <kendo-textbox
                  class="!t-border-[#ccc] t-h-[33.6px] !t-w-[4rem]"
                  size="large"
                  value="NA"
                  [disabled]="true">
                </kendo-textbox>
                }
              </div>
              <div class="t-ml-2 t-flex-auto">
                / {{ totalHitCount$ | async }}
              </div>
            </div>
          </div>
        </ng-container>

        <button
          *ngIf="
            !isTagPanelPopout && !isViewerPanelPopout && !isReviewPanelPopout
          "
          kendoButton
          #parentFullscreen
          class="!t-p-[0.3rem] t-mr-[-12px]"
          fillMode="clear"
          title="Fullscreen"
          size="none"
          (click)="onFullscreenClick('REVIEW')">
          <span
            class="t-bg-[#1EBADC]"
            [parentElement]="parentFullscreen.element"
            venioSvgLoader
            hoverColor="#FFBB12"
            color="#1EBADC"
            [svgUrl]="'assets/svg/icon-review-fullscreen.svg'"
            height="1.2rem"
            width="1.2rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
      </div>
    </div>
  </div>

  <!-- Main content-->
  <div class="t-flex t-h-[calc(100vh-4.9rem)] t-w-full">
    <div class="t-flex t-flex-1" *ngIf="showViewer">
      <div class="t-flex t-border t-border-1 t-border-[#dbdbdb]">
        <!-- @TODO: action bar is incomplete will be added in future -->
        <!-- <ng-container
          [ngComponentOutlet]="
            actionbarContainerComponent | async
          "></ng-container> -->
      </div>

      <div
        class="t-flex t-flex-col t-flex-1 t-relative t-border t-border-l-0 t-border-r-0 t-border-b-0 t-border-t-1 t-border-[#dbdbdb] t-pt-[0.31rem]">
        <div
          class="t-flex t-absolute t-top-1.5 t-right-2 t-z-10"
          *ngIf="
            !isTagPanelPopout && !isViewerPanelPopout && !isReviewPanelPopout
          "
          kendoTooltip>
          <button
            kendoButton
            #parentFullscreen
            class="!t-p-[0.3rem]"
            fillMode="clear"
            title="Fullscreen"
            size="none"
            (click)="onFullscreenClick('VIEWER')">
            <span
              [parentElement]="parentFullscreen.element"
              venioSvgLoader
              hoverColor="#FFBB12"
              [svgUrl]="'assets/svg/icon-review-fullscreen.svg'"
              height="1.2rem"
              width="1.2rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
        </div>
        <div class="t-w-full t-h-full t-block t-relative">
          <ng-container
            *ngComponentOutlet="viewerComponent | async"></ng-container>
          <!-- <venio-viewer-component> </venio-viewer-component>             -->
        </div>
      </div>
    </div>

    <div
      *ngIf="showTags"
      [ngClass]="{ 't-flex-auto': !showViewer, 't-flex-none': true }"
      class="t-flex-wrap t-w-2/6 t-border t-border-1 t-border-[#dbdbdb]">
      <ng-container
        [ngComponentOutlet]="
          utilityPanelContainerComponent | async
        "></ng-container>
    </div>
  </div>
  <div kendoDialogContainer></div>
</div>
