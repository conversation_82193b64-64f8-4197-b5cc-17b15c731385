import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'

import { DocumentCodingRoutingModule } from './document-coding-routing.module'
import { DocumentCodingContainerComponent } from './document-coding-container/document-coding-container.component'
import { DocumentCodingComponent } from './document-coding/document-coding.component'
import { MultivalueCodingFieldContainerComponent } from './multivalue-coding-field/multivalue-coding-field-container/multivalue-coding-field-container.component'

@NgModule({
  declarations: [DocumentCodingContainerComponent],
  imports: [
    CommonModule,
    DocumentCodingRoutingModule,
    DocumentCodingComponent,
    MultivalueCodingFieldContainerComponent,
  ],
})
export class DocumentCodingModule {}
