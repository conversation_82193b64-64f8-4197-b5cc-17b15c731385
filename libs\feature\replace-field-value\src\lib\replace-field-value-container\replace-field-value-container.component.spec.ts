import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReplaceFieldValueContainerComponent } from './replace-field-value-container.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import {
  DocumentsFacade,
  FieldFacade,
  ReplaceFieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { VenioNotificationService } from '@venio/feature/notification'
import { provideRouter } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'

describe('ReplaceFieldValueComponent', () => {
  let component: ReplaceFieldValueContainerComponent
  let fixture: ComponentFixture<ReplaceFieldValueContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReplaceFieldValueContainerComponent, NoopAnimationsModule],
      providers: [
        ReplaceFieldFacade,
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        NotificationService,
        VenioNotificationService,
        DialogRef,
        provideMockStore({}),
        provideRouter([]),
        provideHttpClient(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReplaceFieldValueContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
