import {
  AfterViewInit,
  ChangeDetector<PERSON>ef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule, NgComponentOutlet } from '@angular/common'
import {
  SelectEvent,
  TabStripComponent,
  TabStripModule,
} from '@progress/kendo-angular-layout'
import {
  DialogContentBase,
  DialogRef,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import {
  FieldFacade,
  OverlayCustomFieldsUiType,
  ReplaceFieldFacade,
  UserRights,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-replace-field-value',
  standalone: true,
  imports: [
    CommonModule,
    TabStripModule,
    DialogsModule,
    NgComponentOutlet,
    IndicatorsModule,
    ButtonsModule,
    SvgLoaderDirective,
  ],
  templateUrl: './replace-field-value-container.component.html',
  styleUrl: './replace-field-value-container.component.scss',
})
export class ReplaceFieldValueContainerComponent
  extends DialogContentBase
  implements OnInit, AfterViewInit, OnDestroy
{
  private isRefreshNeeded = false

  public selectedTab = ''

  public ReplaceTabType = ReplaceTabType

  public replaceTabType = ReplaceTabType.FIND_AND_REPLACE

  public UserRights = UserRights

  public isReplacing = this.replaceFieldFacade.isReplaceInProgress$

  public findAndRepaceSvgUrl = 'assets/svg/icon-material-find-replace.svg'

  public mergeUi = {
    uiInputType: OverlayCustomFieldsUiType.MERGE,
  }

  public priorityUi = {
    uiInputType: OverlayCustomFieldsUiType.PRIORITY,
  }

  public fieldValueComponent = import(
    '../field-value/field-value.component'
  ).then(({ FieldValueComponent }) => FieldValueComponent)

  public mergedFieldComponent = import(
    '../merged-priority-field-value/merged-priority-field-value.component'
  ).then(
    ({ MergedPriorityFieldValueComponent }) => MergedPriorityFieldValueComponent
  )

  public priorityFieldComponent = import(
    '../merged-priority-field-value/merged-priority-field-value.component'
  ).then(
    ({ MergedPriorityFieldValueComponent }) => MergedPriorityFieldValueComponent
  )

  @ViewChild('mainTabStrip')
  public mainTabStrip: TabStripComponent

  @ViewChild(NgComponentOutlet, { static: false })
  public ngComponentOutlet: NgComponentOutlet

  // a little hacky way to get the instance of the component, but it works
  // could not find another way to get the instance of the component
  private get ngComponentOutletInstance(): any {
    return this.ngComponentOutlet
      ? this.ngComponentOutlet['_componentRef']?.instance
      : null
  }

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  constructor(
    public dialog: DialogRef,
    public cdr: ChangeDetectorRef,
    private replaceFieldFacade: ReplaceFieldFacade,
    private fieldFacade: FieldFacade,
    private activatedRoute: ActivatedRoute
  ) {
    super(dialog)
  }

  public ngOnInit(): void {
    this.fetchFieldsAndDelimiter()
    this.fetchDelimiters()
    this.#activateInitialTab()

    this.replaceFieldFacade.selectReplacesResponse$.subscribe(
      (response: ResponseModel) => {
        if (response?.status?.toLowerCase() === 'success') {
          this.isRefreshNeeded = true
        }
      }
    )
  }

  #activateInitialTab(): void {
    setTimeout(() => {
      this.cdr.markForCheck()
      this.mainTabStrip.selectTab(0)
      this.#resetControlByTitle(this.mainTabStrip.tabs.first.title)
    })
  }

  private fetchFieldsAndDelimiter(): void {
    this.fieldFacade.fetchAllVenioFields()
    this.replaceFieldFacade.fetchCustomFields(this.projectId)
    this.replaceFieldFacade.fetchComponentFields(this.projectId)
  }

  private fetchDelimiters(): void {
    this.replaceFieldFacade.fetchDelimiters(this.projectId)
  }

  public ngOnDestroy(): void {
    this.replaceFieldFacade.resetReplaceState()
  }

  public cancel(): void {
    this.dialog.close({ isRefreshNeeded: this.isRefreshNeeded })
  }

  public replace(): void {
    if (this.ngComponentOutlet) {
      this.ngComponentOutletInstance?.onReplaceClicked()
    }
  }

  public onTabSelect(e: SelectEvent): void {
    this.#resetControlByTitle(e.title)
  }

  #resetControlByTitle(title: string): void {
    if (title === 'Find & Replace') {
      this.replaceTabType = ReplaceTabType.FIND_AND_REPLACE
    } else if (title === 'Merge Field Value') {
      this.replaceTabType = ReplaceTabType.MERGE
    } else {
      this.replaceTabType = ReplaceTabType.PRIORITY
    }
  }
}

export enum ReplaceTabType {
  FIND_AND_REPLACE = 'FIND_AND_REPLACE',
  MERGE = 'MERGE',
  PRIORITY = 'PRIORITY',
}
