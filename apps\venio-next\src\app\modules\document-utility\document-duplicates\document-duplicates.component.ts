import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Inject,
  <PERSON><PERSON><PERSON>,
  OnD<PERSON>roy,
  OnInit,
  TrackByFunction,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { ActivatedRoute } from '@angular/router'
import {
  DocumentMetadata,
  DuplicateDocument,
  DuplicatePayloadModel,
  ReviewPanelFacade,
  ReviewPanelSelectedDocumentModel,
} from '@venio/data-access/document-utility'
import {
  TempTableResponseModel,
  SearchFacade,
  DocumentsFacade,
  FieldFacade,
  ReviewPanelType,
  CompositeLayoutState,
} from '@venio/data-access/review'
import { Subject, take, filter, takeUntil, combineLatest } from 'rxjs'
import { cloneDeep, isEqual } from 'lodash'
import {
  AppIdentitiesTypes,
  MessageType,
  WINDOW,
} from '@venio/data-access/iframe-messenger'
import { LocalStorage } from '@venio/shared/storage'
import { environment } from '@venio/shared/environments'
import { PageControlActionType } from '@venio/shared/models/constants'
import {
  GridComponent,
  GridItem,
  GridModule,
} from '@progress/kendo-angular-grid'
import { eyeIcon } from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-document-duplicates',
  standalone: true,
  imports: [
    CommonModule,
    TreeListModule,
    GridModule,
    IndicatorsModule,
    TooltipModule,
    IconsModule,
    SvgLoaderDirective,
  ],
  templateUrl: './document-duplicates.component.html',
  styleUrl: './document-duplicates.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentDuplicatesComponent
  implements OnInit, OnDestroy, AfterViewChecked
{
  public documentDuplicates: DuplicateDocument[]

  public tempTables: TempTableResponseModel

  public isFieldsExists = true

  public selectedDocuments: number[]

  public currentDocument: number

  public allFileIds: number[]

  private sortedMetadataFields: string[]

  private defaultFieldIds: number[]

  public venioFieldIds: number[]

  private customFieldIds: number[]

  public expandedKeys: number[] = []

  /**
   * List of metadata to show in the treelist.
   */
  public headers: string[]

  public selectedFields: string[] = []

  public icons = {
    eyeIcon: eyeIcon,
  }

  public isDuplicateLoading$ = this.reviewPanelFacade.selectIsDuplicateLoading$

  public unsubscribed$: Subject<void> = new Subject<void>()

  public get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  public duplicateTreeTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['fileId'] as TrackByFunction<GridItem>

  public rowClass = (args): { [className: string]: boolean } => ({
    'k-selected': args.dataItem.fileId === this.currentDocument,
  })

  @ViewChild(GridComponent)
  public treelist: GridComponent

  // store the last known width of the treelist
  public lastKnownWidth = 0

  constructor(
    private reviewPanelFacade: ReviewPanelFacade,
    private searchFacade: SearchFacade,
    private fieldFacade: FieldFacade,
    private documentsFacade: DocumentsFacade,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    @Inject(WINDOW) private windowRef: Window,
    private ngZone: NgZone
  ) {}

  public ngOnInit(): void {
    this.#getDuplicateData()
    this.#selectDuplicateData()
    this.fieldFacade.notifyFieldChanges.next()

    this.changeDetectorRef.markForCheck()
  }

  // Workaround solution for the issue where the treelist does not fit/resize columns and scrollbar glitch even after with ngAfterViewInit.
  public ngAfterViewChecked(): void {
    if (this.treelist && this.lastKnownWidth === 0) {
      const currentWidth = this.treelist.wrapper.nativeElement.offsetWidth
      // Call fitColumns when the last known width is less than 10.
      if (this.lastKnownWidth < 10) {
        this.fitColumns()
        this.lastKnownWidth = currentWidth
      }
    }
  }

  #getDuplicateData(): void {
    combineLatest([
      this.documentsFacade.getSelectedDocuments$,
      this.searchFacade.getSearchTempTables$,
      this.fieldFacade.notifyFieldChanges,
    ])
      .pipe(
        filter(([selectedDocuments]) => Boolean(selectedDocuments?.length > 0)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([selectedDocuments, tempTables]) => {
        const hasFieldsChanged = this.#updateFields()
        const hasFileIdChanged = !isEqual(
          selectedDocuments,
          this.selectedDocuments
        )
        if (!hasFieldsChanged && !hasFileIdChanged) return
        this.tempTables = tempTables
        this.selectedDocuments = selectedDocuments
        this.currentDocument = selectedDocuments[0]
        this.#fetchDuplicateData()
      })
  }

  #fetchDuplicateData(): void {
    if (this.currentDocument < 0) {
      this.#resetPanelData()
      return
    }
    const duplicatePayload: DuplicatePayloadModel = {
      fileId: this.currentDocument,
      projectId: this.projectId,
      tempTable: this.tempTables,
      venioFieldIds: this.venioFieldIds,
      customFieldIds: this.customFieldIds,
    }
    this.reviewPanelFacade.getDuplicateDocumentdata(duplicatePayload)
  }

  #selectDuplicateData(): void {
    this.reviewPanelFacade.selectDuplicateDocument$
      .pipe(
        filter((duplicateDocument) => duplicateDocument?.length > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((duplicateDocument) => {
        this.#showHideFields(this.selectedFields)
        if (!this.isFieldsExists) return
        this.documentDuplicates = cloneDeep(duplicateDocument)
        const metadata: DocumentMetadata[] =
          duplicateDocument[0].metadata.slice()

        this.headers = this.layoutState
          .getFieldsSortedByLayoutOrder(
            metadata,
            'key',
            ReviewPanelType.Duplicates
          )
          .map((field: any) => field.key)
          .filter((f) => this.selectedFields.includes(f))
        this.documentDuplicates?.forEach((doc) => {
          for (const field of this.headers) {
            doc[field] = doc?.metadata?.find(
              (data) => data.key === field
            )?.value
          }
        })
        this.fitColumns()
        this.changeDetectorRef.markForCheck()
      })
  }

  public onDataStateChange(): void {
    this.fitColumns()
  }

  public fitColumns(): void {
    this.ngZone.onStable
      .asObservable()
      .pipe(take(1))
      .subscribe(() => {
        this.treelist.autoFitColumns()
      })
  }

  public onDetailsClicked(dataItem): void {
    const fileId = dataItem?.fileId
    const selectReviewPanelDocument: ReviewPanelSelectedDocumentModel = {
      currentfileId: this.currentDocument,
      documentNo: fileId,
      isDocumentExistsInSearchScope: dataItem.isFileExistsSearchScope,
    }

    this.reviewPanelFacade.setSelectedReviewPanelDocument(
      selectReviewPanelDocument
    )
    // Send an action event from the popout window to update data in the parent window.
    if (this.isTagPanelPopout) {
      this.#sendDocumentDuplicatedActionEvent(selectReviewPanelDocument)
      return
    }
  }

  /**
   * Sends a parent-child action event to the parent window.
   * @param {ReviewPanelSelectedDocumentModel} selectReviewPanelDocument - The selected document to include in the event payload.
   * @returns {void} This method does not return anything.
   */
  #sendDocumentDuplicatedActionEvent(
    selectReviewPanelDocument: ReviewPanelSelectedDocumentModel
  ): void {
    // Popout window is not open then return
    if (!this.isTagPanelPopout) return

    this.windowRef.opener.postMessage(
      {
        type: 'MICRO_APP_DATA_CHANGE',
        payload: {
          type: MessageType.WINDOW_CHANGE,
          content: {
            selectReviewPanelDocument: selectReviewPanelDocument,
            pageControlActionType: PageControlActionType.DUPLICATE_DOCUMENT,
          },
        },
        eventTriggeredBy: AppIdentitiesTypes.UTILITY_PANEL_ACTION,
        iframeIdentity: AppIdentitiesTypes.UTILITY_PANEL,
        eventTriggeredFor: 'ALL_WINDOW',
      },
      environment.allowedOrigin
    )
  }

  #updateFields(): boolean {
    const selectedFields = this.layoutState
      .userSelectedLayout()
      .layoutPanels.find((p) => p.panelName === ReviewPanelType.Duplicates)
      ?.fields.filter((f) => f.isSelected)
    const hasFieldsChanged = !isEqual(
      this.selectedFields,
      selectedFields.map((f) => f.fieldName)
    )
    this.selectedFields = selectedFields.map((f) => f.fieldName)

    this.venioFieldIds = selectedFields
      .filter((f) => !f.isCustomField)
      .map((f) => f.fieldId)
    this.customFieldIds = selectedFields
      .filter((f) => f.isCustomField)
      .map((f) => f.fieldId)
    if (this.customFieldIds?.length === 0) this.customFieldIds = [-1]
    return hasFieldsChanged
  }

  #showHideFields(visibleMetaDataFields: string[]): void {
    this.isFieldsExists = Boolean(visibleMetaDataFields?.[0])
    // clear the headers and documentDuplicates if no fields are visible
    if (!this.isFieldsExists) {
      this.#resetPanelData()
    }
  }

  #resetPanelData(): void {
    this.headers = []
    this.documentDuplicates = []
    this.changeDetectorRef.markForCheck()
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
