<kendo-grid
  #grid
  *ngIf="isNDDFormed(); else noNDDDataTemplate"
  [kendoGridBinding]="documentNearDuplicates"
  [loading]="isNearDuplicateLoading$ | async"
  scrollable="virtual"
  [sortable]="true"
  [autoSize]="true"
  [rowClass]="rowClass"
  [resizable]="true"
  (dataStateChange)="onDataStateChange()">
  <ng-template kendoGridNoRecordsTemplate>
    <div class="t-flex t-h-min t-w-full">
      <span class="t-text-[#000000BC] t-text-[16px]">No records found</span>
    </div>
  </ng-template>
  <kendo-grid-column
    title="Details"
    [headerClass]="[
      't-text-primary',
      totalCount() === 0 ? 't-min-w-[150px]' : ''
    ]"
    [width]="250"
    [minResizableWidth]="100">
    <ng-template kendoGridHeaderTemplate let-column>
      <span kendoTooltip title="Details">Details</span>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-flex t-w-full t-pl-3.5">
        <span
          class="t-cursor-pointer"
          (click)="onDetailsClicked(dataItem)"
          venioSvgLoader
          kendoTooltip
          title="View"
          hoverColor="#FFBB12"
          color="#979797"
          [svgUrl]="'assets/svg/icon-show-fields.svg'"
          height="1rem"
          width="1rem">
          <kendo-loader size="small" />
        </span>
      </div>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column
    *ngFor="let field of headers"
    [field]="field"
    headerClass="t-text-primary">
    <ng-template kendoGridHeaderTemplate let-column>
      <span kendoTooltip [title]="field">{{ field }}</span>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      <span
        kendoTooltip
        [title]="dataItem[field]"
        [ngClass]="getCellClass(field, dataItem)"
        >{{ dataItem[field] }}</span
      >
    </ng-template>
  </kendo-grid-column>
</kendo-grid>

<ng-template #noNDDDataTemplate>
  <div class="t-block t-place-content-center t-p-2 t-w-full">
    <div
      class="t-p-2 t-text-sm t-overflow-hidden t-text-center t-w-full t-text-white t-bg-[#EAB676] t-border-r-0 t-rounded">
      NDD is not performed, please perform NDD to view the information.
    </div>
  </div>
</ng-template>
