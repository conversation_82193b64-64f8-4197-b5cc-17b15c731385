<div class="t-flex t-flex-col t-h-full v-custom-grey-bg">
  <div class="t-pb-1 t-pt-1">
    <div
      class="t-flex t-flex-row t-border-b t-border-solid t-border-[#DADADA] t-py-2">
      <div class="t-w-1/4 t-m-1">
        <kendo-dropdownlist
          id="status-option"
          class=""
          [data]="statusOption"
          [valuePrimitive]="true"
          textField="name"
          valueField="value"
          [(ngModel)]="statusOptionSelected">
        </kendo-dropdownlist>
      </div>
      <div class="t-flex t-w-3/4 t-m-1">
        <button
          #parentRefresh
          kendoButton
          (click)="onRefreshClicked()"
          class="t-p-1 t-w-[2.56rem] t-h-[2.125rem] t-border t-border-[#9BD2A7] t-rounded-[2px] t-opacity-100 hover:!t-bg-[#9AD3A6]"
          fillMode="outline"
          title="Refresh">
          <span
            [parentElement]="parentRefresh.element"
            venioSvgLoader
            [svgUrl]="refreshSvgUrl"
            hoverColor="#FFFFFF"
            height="1.1rem"
            width="1.3rem"></span>
        </button>
      </div>
    </div>
    <div class="t-flex t-flex-row t-h-full">
      <div class="t-h-full" *ngIf="statusOptionSelected === 'PRINT_STATUS'">
        <ng-container
          *ngComponentOutlet="printStatusComponent | async"></ng-container>
      </div>
      <div
        class="t-h-full"
        *ngIf="statusOptionSelected === 'NATIVE_DOWNLOAD_STATUS'">
        <ng-container
          *ngComponentOutlet="nativeStatusComponent | async"></ng-container>
      </div>
    </div>
  </div>
</div>
