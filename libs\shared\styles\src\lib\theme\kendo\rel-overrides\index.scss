// Compatible with @progress/kendo-theme-fluent v.6.4.0
@use '@progress/kendo-theme-fluent/scss/all.scss';
/* src/theme/_kendo-overrides.scss  – imported before Tailwind */
/* libs/shared/styles/src/lib/theme/kendo/rel-overrides/index.scss */

// In future, we need to SCSS way to override Kendo styles removing the !important to avoid untraceable issues.
//@use "@progress/kendo-theme-fluent/scss/all.scss" as * with (
//  $kendo-table-sizes: (
//    sm: (
//      cell-padding-x: 0.5rem,
//      cell-padding-y: 0.25rem
//    ),
//    md: (
//      cell-padding-x: 0.5rem,
//      cell-padding-y: 0.25rem
//    ),
//    lg: (
//      cell-padding-x: 0.5rem,
//      cell-padding-y: 0.25rem
//    )
//  )
//);

@import 'tokens';
@import 'custom-tokens';
@import 'overrides';
@import 'custom_overrides';
