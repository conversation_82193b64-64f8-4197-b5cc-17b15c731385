import { Injectable } from '@angular/core'
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpResponse,
} from '@angular/common/http'
import { Observable, of, throwError } from 'rxjs'
import { tap, catchError, shareReplay, finalize } from 'rxjs/operators'
import <PERSON>ie, { Table } from 'dexie'
import CryptoJS from 'crypto-js'

// A lightweight LRU cache for in-memory caching
export class LRUCache<K, V> {
  private cache: Map<K, V>

  constructor(private capacity = 50) {
    this.cache = new Map<K, V>()
  }

  public get(key: K): V | undefined {
    if (!this.cache.has(key)) {
      return undefined
    }
    const value = this.cache.get(key)
    // Refresh usage
    this.cache.delete(key)
    this.cache.set(key, value)
    return value
  }

  public set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key)
    }
    this.cache.set(key, value)
    if (this.cache.size > this.capacity) {
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }
  }

  public delete(key: K): boolean {
    return this.cache.delete(key)
  }

  public clear(): void {
    this.cache.clear()
  }
}

/**
 * Represents a cached SVG entry in Dexie.
 */
export interface SvgCacheEntry {
  /** The request URL (cache key). */
  url: string
  /** Sanitized SVG content. */
  sanitizedSvg: string
  /** SHA-256 hash of sanitizedSvg. */
  hash: string
  /** Timestamp for expiration checks. */
  timestamp: number
}

/**
 * Interceptor that:
 * 1. Identifies GET requests ending in .svg (ignoring query params).
 * 2. Caches them in memory (LRU) and Dexie.
 * 3. Sanitizes the SVG to remove malicious content.
 * 4. Hashes the sanitized text to detect tampering in Dexie.
 * 5. Ensures concurrency: if multiple subscribers request the same URL simultaneously,
 *    only one network call is made.
 */
@Injectable({ providedIn: 'root' })
export class SvgRequestInterceptor extends Dexie implements HttpInterceptor {
  private readonly oneSecond = 1000

  private readonly oneMinute = 60 * this.oneSecond

  private readonly oneHour = 60 * this.oneMinute

  private readonly oneDay = 24 * this.oneHour

  public SVG_CACHE_EXPIRATION_MS = 30 * this.oneDay

  private readonly tableName = 'VenioSVGContentCache'

  /** Dexie table for persistent caching. */
  public VenioSVGContentCache: Table<SvgCacheEntry, string>

  /**
   * In-memory LRU for quick lookups within the same session.
   * Here we set capacity to 50; adjust or make configurable as you see fit.
   */
  public memoryCache = new LRUCache<string, SvgCacheEntry>(50)

  /**
   * Map of ongoing requests to share responses for concurrent subscribers.
   */
  private ongoingRequests = new Map<string, Observable<HttpEvent<any>>>()

  constructor() {
    // Name of your Dexie DB
    super('VenioCache')
    // Create Dexie store
    this.version(2).stores({ [this.tableName]: 'url' })
    this.VenioSVGContentCache = this.table(this.tableName)
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    //  Skip if not GET
    if (req.method !== 'GET') {
      return next.handle(req)
    }

    // Derive a minimal “clean” URL for checking (excluding query params).
    const cleanedUrl = req.url.split('?')[0].toLowerCase()

    // if the requests was made for /assets/ folder, we don't want to cache it
    if (!cleanedUrl.includes('assets/')) {
      return next.handle(req)
    }

    // Skip if not .svg
    if (!cleanedUrl.endsWith('.svg')) {
      return next.handle(req)
    }

    // Build cache key (excluding query params by default)
    const cacheKey = this.getFullCacheKey(req.url)

    // Return next if not an .svg request
    if (!this.isSvgRequest(cacheKey)) {
      return next.handle(req)
    }

    // 1) Attempt quick return from memory cache
    const memEntry = this.memoryCache.get(cacheKey)
    if (memEntry && this.isFresh(memEntry.timestamp)) {
      // Double-check tampering
      const recomputedHash = this.computeHash(memEntry.sanitizedSvg)
      if (recomputedHash === memEntry.hash) {
        return of(
          new HttpResponse({
            body: memEntry.sanitizedSvg,
            status: 200,
            url: req.url,
          })
        )
      }
      // Tampered => remove it
      this.memoryCache.delete(cacheKey)
    }

    // 2) Check Dexie if memory fails
    return new Observable<HttpEvent<any>>((observer) => {
      // concurrency check
      if (this.ongoingRequests.has(cacheKey)) {
        const ongoing$ = this.ongoingRequests.get(cacheKey)
        ongoing$.subscribe({
          next: (evt: HttpEvent<unknown>) => observer.next(evt),
          error: (err: unknown) => observer.error(err),
          complete: () => observer.complete(),
        })
        return
      }

      // Attempt Dexie read
      this.VenioSVGContentCache.get(cacheKey)
        .then((entry) => {
          // If Dexie has a valid entry, return it
          if (entry && this.isFresh(entry.timestamp)) {
            const dbHash = this.computeHash(entry.sanitizedSvg)
            if (dbHash === entry.hash) {
              // Save to memory for quick future retrieval
              this.memoryCache.set(cacheKey, entry)
              observer.next(
                new HttpResponse({
                  body: entry.sanitizedSvg,
                  status: 200,
                  url: req.url,
                })
              )
              observer.complete()
              return null
            }
            // Tampered => remove
            this.VenioSVGContentCache.delete(cacheKey)
            return 'fetch'
          }
          // If no valid Dexie entry, fetch from network
          return 'fetch'
        })
        .then((fetchOrNull) => {
          // If we need a new network request:
          if (fetchOrNull === 'fetch') {
            const network$ = next.handle(req).pipe(
              tap((evt) => {
                if (
                  evt instanceof HttpResponse &&
                  typeof evt.body === 'string'
                ) {
                  const sanitized = this.sanitizeSvg(evt.body)
                  // Only store if sanitization didn't fail
                  if (sanitized) {
                    const newEntry: SvgCacheEntry = {
                      url: cacheKey,
                      sanitizedSvg: sanitized,
                      hash: this.computeHash(sanitized),
                      timestamp: Date.now(),
                    }
                    // Save in Dexie + memory
                    this.VenioSVGContentCache.put(newEntry).catch(
                      (err: unknown) =>
                        console.error('Error saving Dexie entry:', err)
                    )
                    this.memoryCache.set(cacheKey, newEntry)
                  }
                }
              }),
              catchError((error: unknown) => {
                // If fails, remove from concurrency map
                // We could also remove partial entries from Dexie if they exist
                return throwError(() => error)
              }),
              shareReplay({ bufferSize: 1, refCount: true }),
              finalize(() => {
                // Once done, remove from concurrency map
                this.ongoingRequests.delete(cacheKey)
              })
            )

            // Store this in-flight request
            this.ongoingRequests.set(cacheKey, network$)
            network$.subscribe({
              next: (evt: HttpEvent<unknown>) => observer.next(evt),
              error: (err: unknown) => observer.error(err),
              complete: () => observer.complete(),
            })
          } else if (fetchOrNull === null) {
            // Already returned a cached entry
            observer.complete()
          }
        })
        .catch((err: unknown) => {
          this.ongoingRequests.delete(cacheKey)
          observer.error(err)
        })
    })
  }

  /**
   * Helper: true if .svg after removing query string from the URL.
   */
  private isSvgRequest(url: string): boolean {
    const cleanUrl = url.split('?')[0]
    return cleanUrl.endsWith('.svg')
  }

  /**
   * Helper: produce the cache key for the request.
   * By default, we'll exclude query params to avoid duplicates.
   */
  private getFullCacheKey(fullUrl: string): string {
    // Example approach: remove query params from the URL to form a simpler key
    return fullUrl.split('?')[0]
  }

  /**
   * Checks if the entry was created within the expiration window.
   */
  private isFresh(timestamp: number): boolean {
    return Date.now() - timestamp < this.SVG_CACHE_EXPIRATION_MS
  }

  /**
   * Compute a SHA-256 hash of the sanitized SVG text.
   */
  private computeHash(content: string): string {
    return CryptoJS.SHA256(content).toString()
  }

  /**
   * Sanitizes the raw SVG to remove malicious scripts/attributes.
   */
  private sanitizeSvg(rawSvg: string): string {
    try {
      const parser = new DOMParser()
      const doc = parser.parseFromString(rawSvg, 'image/svg+xml')
      if (doc.querySelector('parsererror')) {
        // Invalid SVG => return empty
        return ''
      }

      // Remove tags known to be dangerous
      const forbiddenTags = [
        'script',
        'foreignObject',
        'iframe',
        'embed',
        'object',
      ]
      for (const tag of forbiddenTags) {
        doc.querySelectorAll(tag).forEach((el) => el.remove())
      }

      // Remove suspicious attributes
      doc.querySelectorAll('*').forEach((element) => {
        Array.from(element.attributes).forEach((attr) => {
          const name = attr.name.toLowerCase()
          const value = attr.value.toLowerCase()
          // Remove event handlers
          if (name.startsWith('on')) {
            element.removeAttribute(attr.name)
          }
          // Remove xlink:href or href if referencing javascript:
          if (
            (name === 'xlink:href' || name === 'href') &&
            value.startsWith('javascript:')
          ) {
            element.removeAttribute(attr.name)
          }
        })
      })

      // Return sanitized SVG
      return doc.documentElement.outerHTML
    } catch (error: unknown) {
      return ''
    }
  }
}
