import { ComponentFix<PERSON>, TestBed } from '@angular/core/testing'
import { LayoutContainerComponent } from './layout-container.component'
import { CUSTOM_ELEMENTS_SCHEMA, PLATFORM_ID } from '@angular/core'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import {
  CaseInfoFacade,
  FieldFacade,
  SearchFacade,
  StartupsFacade,
  ViewFacade,
  CompositeLayoutFacade,
  CompositeLayoutState,
  ReviewsetFacade,
  ReviewSetStateService,
} from '@venio/data-access/review'
import { ActivatedRoute, Router } from '@angular/router'
import { provideMockStore } from '@ngrx/store/testing'
import { BehaviorSubject, of, Subject } from 'rxjs'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import {
  AppIdentitiesTypes,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
  IframeMessengerFacade,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'
import { VenioNotificationService } from '@venio/feature/notification'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import {
  UserFacade,
  ValidatorService,
  ModuleLoginFacade,
  ModuleLoginStateService,
} from '@venio/data-access/common'
import {
  DocumentTagService,
  ReviewPanelViewState,
  UtilityPanelFacade,
} from '@venio/data-access/document-utility'
import { ConfirmationDialogService } from '../../../services/confirmation-dialog-service'

describe('LayoutContainerComponent', () => {
  let component: LayoutContainerComponent
  let fixture: ComponentFixture<LayoutContainerComponent>
  let queryParamsSubject: BehaviorSubject<any>
  let mockIframeMessengerFacade: any
  let mockIframeMessengerService: any
  let mockSearchFacade: any
  let mockViewFacade: any
  let mockReviewsetFacade: any
  let mockModuleLoginFacade: any
  let mockModuleLoginStateService: any
  let mockReviewSetStateService: any
  let mockRouter: any

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({
      projectId: 123,
      reviewSetId: 456,
    })

    // Create comprehensive mocks
    mockIframeMessengerFacade = {
      selectIframeMessengerContent$: jest.fn().mockReturnValue(of(null)),
    }

    mockIframeMessengerService = {
      sendMessage: jest.fn(),
    }

    mockSearchFacade = {
      selectSearchFormValues$: of({}),
      getMainSearchBreadcrumbs$: of([]),
      getConditionSearchBreadcrumbs$: of([]),
      getFilterSearchBreadcrumbs$: of([]),
      getReviewSetBatchId$: of(0),
      search: jest.fn(),
      resetSearchState: jest.fn(),
      setSearchDupOption: jest.fn(),
      setIncludePC: jest.fn(),
    }

    mockViewFacade = {
      selectUserDefaultView$: of(null),
      selectIsUserDefaultViewLoading$: of(false),
      fetchUserDefaultView: jest.fn(),
      resetView: jest.fn(),
      isViewManuallyChanged: jest.fn().mockReturnValue(false),
    }

    mockReviewsetFacade = {
      fetchReviewSetBasicInfo$: jest
        .fn()
        .mockReturnValue(of({ data: { reviewSetId: 456 } })),
      checkBatchReviewCompletedAction$: of(0),
      checkoutBatchReviewSetAction: new Subject(),
      fetchReviewSetBatchInfo$: jest
        .fn()
        .mockReturnValue(of({ data: { remainingFiles: 0, batchId: 1 } })),
      checkInReviewBatch$: jest
        .fn()
        .mockReturnValue(of({ message: 'Success' })),
    }

    mockModuleLoginFacade = {
      createNewModuleLogin: jest.fn().mockReturnValue(of({})),
      updateExistingModuleLogin: jest.fn().mockReturnValue(of({})),
    }

    mockModuleLoginStateService = {
      moduleLoginId: jest.fn().mockReturnValue(1),
      previousReviewSetId: jest.fn().mockReturnValue(456),
      updateProjectId: jest.fn(),
      updatePreviousReviewSetId: jest.fn(),
    }

    mockReviewSetStateService = {
      reviewSetId: jest.fn().mockReturnValue(456),
      reviewSetBasicInfo: { set: jest.fn() },
      reset: jest.fn(),
      isBatchReview: jest.fn().mockReturnValue(false),
      batchId: { set: jest.fn() },
      reviewsetBatchInfo: { set: jest.fn() },
    }

    mockRouter = {
      parseUrl: jest.fn().mockReturnValue({
        root: {
          children: {
            primary: {
              segments: [{ path: 'documents' }],
            },
          },
        },
      }),
      url: '/documents',
    }

    await TestBed.configureTestingModule({
      imports: [LayoutContainerComponent, BrowserAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: FieldFacade,
          useValue: {
            fetchAllVenioFields: jest.fn(),
            fetchReviewPanelDefaltFields: jest.fn(),
            fetchAllPermittedFields: jest.fn(),
            fetchAllCustomWithPredefinedFields: jest.fn(),
            fetchAllCustomFields: jest.fn(),
            fetchUserLayoutId: jest.fn(),
            resetField: jest.fn(),
          },
        },
        {
          provide: CaseInfoFacade,
          useValue: {
            isProjectManuallyChanged: jest.fn().mockReturnValue(false),
          },
        },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: ViewFacade, useValue: mockViewFacade },
        {
          provide: StartupsFacade,
          useValue: {
            fetchUserRights: jest.fn(),
            fetchDefaultGroups: jest.fn(),
          },
        },
        {
          provide: CompositeLayoutFacade,
          useValue: {
            fetchDefaultLayout$: jest.fn().mockReturnValue(of({ data: {} })),
          },
        },
        {
          provide: CompositeLayoutState,
          useValue: {
            userLayouts: { set: jest.fn() },
            userSelectedLayout: jest.fn().mockReturnValue(null),
          },
        },
        { provide: ReviewsetFacade, useValue: mockReviewsetFacade },
        { provide: ReviewSetStateService, useValue: mockReviewSetStateService },
        { provide: ModuleLoginFacade, useValue: mockModuleLoginFacade },
        {
          provide: ModuleLoginStateService,
          useValue: mockModuleLoginStateService,
        },
        { provide: IframeMessengerFacade, useValue: mockIframeMessengerFacade },
        {
          provide: IframeMessengerService,
          useValue: mockIframeMessengerService,
        },
        { provide: Router, useValue: mockRouter },
        {
          provide: BreadcrumbFacade,
          useValue: {
            selectBreadcrumbStack$: of([]),
            selectCompleteBreadcrumbSyntax$: of(''),
            resetBreadcrumbState: jest.fn(),
            storeBreadcrumbs: jest.fn(),
            updateBreadcrumb: jest.fn(),
          },
        },
        {
          provide: UserFacade,
          useValue: {
            selectCurrentUserSuccessResponse$: of({ data: { userId: 1 } }),
          },
        },
        { provide: ValidatorService, useValue: {} },
        {
          provide: DocumentTagService,
          useValue: { getTagRuleData: jest.fn().mockReturnValue({}) },
        },
        {
          provide: ReviewPanelViewState,
          useValue: { resetDocuemntTagSelection: jest.fn() },
        },
        {
          provide: UtilityPanelFacade,
          useValue: { resetUtilityPanelState: jest.fn() },
        },
        {
          provide: ConfirmationDialogService,
          useValue: {
            showConfirmationDialog: jest.fn().mockReturnValue(of(true)),
          },
        },
        NotificationService,
        VenioNotificationService,
        provideMockStore({}),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: { projectId: 123, reviewSetId: 456 } },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(LayoutContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('hideToolbar signal', () => {
    it('should return false initially', () => {
      expect(component.hideToolbar()).toBeFalsy()
    })

    it('should return true when toolbar hide message is received', () => {
      const mockMessage = {
        content: { toolbar: 'hide' },
      }
      mockIframeMessengerFacade.selectIframeMessengerContent$.mockReturnValue(
        of(mockMessage)
      )

      // Recreate component to trigger signal update
      fixture = TestBed.createComponent(LayoutContainerComponent)
      component = fixture.componentInstance
      fixture.detectChanges()

      expect(component.hideToolbar()).toBeTruthy()
    })

    it('should handle array message format', () => {
      const mockMessage = [{ content: { toolbar: 'hide' } }]
      mockIframeMessengerFacade.selectIframeMessengerContent$.mockReturnValue(
        of(mockMessage)
      )

      fixture = TestBed.createComponent(LayoutContainerComponent)
      component = fixture.componentInstance
      fixture.detectChanges()

      expect(component.hideToolbar()).toBeTruthy()
    })
  })

  describe('isToolbarVisible computed', () => {
    it('should return true when hideToolbar is false', () => {
      expect(component.isToolbarVisible()).toBeTruthy()
    })

    it('should return false when hideToolbar is true', () => {
      const mockMessage = { content: { toolbar: 'hide' } }
      mockIframeMessengerFacade.selectIframeMessengerContent$.mockReturnValue(
        of(mockMessage)
      )

      fixture = TestBed.createComponent(LayoutContainerComponent)
      component = fixture.componentInstance
      fixture.detectChanges()

      expect(component.isToolbarVisible()).toBeFalsy()
    })
  })

  describe('ngOnInit', () => {
    it('should call initial setup methods', () => {
      const sendMessageSpy = jest.spyOn(
        mockIframeMessengerService,
        'sendMessage'
      )
      const updateProjectIdSpy = jest.spyOn(
        mockModuleLoginStateService,
        'updateProjectId'
      )

      component.ngOnInit()

      expect(sendMessageSpy).toHaveBeenCalledWith({
        iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        eventTriggeredFor: 'PARENT_WINDOW',
        eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
        payload: {
          type: MessageType.LAYOUT_CHANGE,
          content: {
            layoutReady: true,
          },
        },
      })
      expect(updateProjectIdSpy).toHaveBeenCalledWith(123)
    })
  })

  describe('Query Parameter Handling', () => {
    it('should initialize with correct project and review set IDs', () => {
      // The component should be initialized with the query params from the mock
      expect(component['projectId']).toBe(123)
      expect(mockModuleLoginStateService.updateProjectId).toHaveBeenCalledWith(
        123
      )
    })

    it('should handle project ID changes correctly', () => {
      const resetViewSpy = jest.spyOn(mockViewFacade, 'resetView')

      // Simulate project ID change by creating a new component with different params
      queryParamsSubject.next({ projectId: 999, reviewSetId: 456 })

      // The component should handle project changes
      expect(resetViewSpy).toHaveBeenCalled()
    })
  })

  describe('Module Login Periodic Updates', () => {
    it('should setup periodic updates for batch review', () => {
      mockReviewSetStateService.isBatchReview.mockReturnValue(true)
      mockModuleLoginStateService.moduleLoginId.mockReturnValue(1)

      const updateSpy = jest.spyOn(
        mockModuleLoginFacade,
        'updateExistingModuleLogin'
      )

      component.ngAfterViewInit()

      // The periodic update should be set up (we can't easily test the interval without fakeAsync)
      expect(updateSpy).toHaveBeenCalled()
    })
  })

  describe('Project ID Changes', () => {
    it('should handle project ID changes and reset states', () => {
      const resetViewSpy = jest.spyOn(mockViewFacade, 'resetView')
      const resetFieldSpy = jest.spyOn(
        TestBed.inject(FieldFacade),
        'resetField'
      )

      queryParamsSubject.next({ projectId: 999, reviewSetId: 456 })

      expect(resetViewSpy).toHaveBeenCalledWith([
        'userDefaultView',
        'selectedViewDefaultExpression',
      ])
      expect(resetFieldSpy).toHaveBeenCalledWith([
        'allCustomFields',
        'fieldPanelMap',
        'venioFieldsPanelMap',
        'customFieldsPanelMap',
      ])
    })
  })

  describe('Batch Checkout Complete Handling', () => {
    it('should handle batch completion with remaining files = 0', () => {
      const batchInfo = { remainingFiles: 0, batchId: 1 }
      mockReviewsetFacade.fetchReviewSetBatchInfo$.mockReturnValue(
        of({ data: batchInfo })
      )

      const confirmationSpy = jest.spyOn(
        TestBed.inject(ConfirmationDialogService),
        'showConfirmationDialog'
      )

      // Trigger batch completion
      mockSearchFacade.getReviewSetBatchId$ = of(1)

      component.ngOnInit()

      expect(confirmationSpy).toHaveBeenCalled()
    })
  })

  describe('Search and Breadcrumb Handling', () => {
    it('should handle search form values correctly', () => {
      const searchFormData = { searchDuplicateOption: 'all', includePC: true }
      mockSearchFacade.selectSearchFormValues$ = of(searchFormData)

      component.ngOnInit()

      expect(component['currentSearchFormData']).toEqual(searchFormData)
    })

    it('should initialize current user correctly', () => {
      // The user should be initialized from the mock data
      expect(component['currentUser']()).toEqual({ userId: 1 })
    })
  })

  describe('Layout and State Management', () => {
    it('should have correct project ID from route params', () => {
      expect(component['projectId']).toBe(123)
    })

    it('should have correct review set ID from state service', () => {
      expect(component['reviewSetId']).toBe(456)
    })

    it('should determine if on review page correctly', () => {
      expect(component['isReviewPage']).toBeTruthy()
    })
  })

  describe('Component Lifecycle', () => {
    it('should clean up subscriptions on destroy', () => {
      const resetSearchSpy = jest.spyOn(mockSearchFacade, 'resetSearchState')

      component.ngOnDestroy()

      expect(resetSearchSpy).toHaveBeenCalledWith([
        'searchFormValues',
        'shouldResetSearchInputControls',
      ])
    })

    it('should handle ngAfterViewInit correctly', () => {
      // Test that ngAfterViewInit executes without errors
      expect(() => component.ngAfterViewInit()).not.toThrow()
    })
  })
})
