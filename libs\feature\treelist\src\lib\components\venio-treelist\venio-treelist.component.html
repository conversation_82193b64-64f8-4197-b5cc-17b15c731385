<kendo-treelist
  [kendoTreeListFlatBinding]="selectionData"
  [parentIdField]="parentIdField"
  [idField]="idField"
  [sortable]="true"
  [height]="height"
  kendoTreeListExpandable
  kendoTreeListSelectable
  (selectionChange)="selectionChange($event)"
  [isSelected]="isSelected"
  [selectable]="selectableSettings"
  [rowClass]="rowClassFn"
  [resizable]="resizable"
  class="t-w-full t-overflow-y-visible t-relative v-custom-even-treelist"
  #treeListWrapper>
  <kendo-treelist-column
    [expandable]="true"
    [width]="20"
    [resizable]="false"
    headerClass="t-text-primary"
    class="!k-text-clip"
    *ngIf="hasParentChildHierarchy"
    [sortable]="false"></kendo-treelist-column>

  <kendo-treelist-column
    field="selectedField"
    [width]="30"
    [resizable]="false"
    headerClass="t-text-primary"
    [sortable]="false">
    <ng-template kendoTreeListHeaderTemplate let-column>
      <input
        type="checkbox"
        class="k-checkbox k-checkbox-md k-rounded-md ng-star-inserted t-mt-[2px]"
        (change)="selectAllClick()"
        [(ngModel)]="selectAllChecked"
        [indeterminate]="selectAllChecked === null"
        [disabled]="!selectionData.length"
        kendoCheckBox />
    </ng-template>

    <ng-template
      kendoTreeListCellTemplate
      let-dataItem
      let-rowIndex="rowIndex"
      let-column="column">
      <input
        type="checkbox"
        class="k-checkbox k-checkbox-md k-rounded-md ng-star-inserted"
        [checked]="isSelected(dataItem)"
        [indeterminate]="isSelected(dataItem) === null"
        (change)="itemSelected(dataItem)"
        [ngClass]="{
          't-ml-5': parentIdField !== 'null' && dataItem[parentIdField] !== null
        }"
        kendoCheckBox />
    </ng-template>
  </kendo-treelist-column>

  <ng-container *ngFor="let column of treelistColumns; let first = first">
    <ng-container *ngIf="column.field">
      <kendo-treelist-column
        [field]="column.field"
        [title]="column.title"
        [width]="column.width"
        [resizable]="resizable"
        [reorderable]="column.reorderable"
        [autoSize]="column.autoSize"
        [hidden]="column.hidden"
        [format]="column.format"
        [sortable]="column.sortable"
        headerClass="t-text-primary"
        [class]="column.cssClass">
        <ng-template kendoTreeListCellTemplate let-dataItem>
          <ng-container *ngIf="!column.columnTemplate">
            <!-- indent only the first column of the child items to properly distinguish between child and parent -->
            <div
              [ngClass]="{
                't-ml-5':
                  first &&
                  parentIdField !== 'null' &&
                  dataItem[parentIdField] !== null
              }">
              {{ dataItem[column.field] }}
            </div>
          </ng-container>
          <ng-container *ngIf="!!column.columnTemplate">
            <ng-container
              *ngTemplateOutlet="
                column.columnTemplate;
                context: { $implicit: dataItem }
              "></ng-container>
          </ng-container>
        </ng-template>
      </kendo-treelist-column>
    </ng-container>
  </ng-container>

  <ng-template kendoTreeListNoRecordsTemplate> No data </ng-template>
</kendo-treelist>
