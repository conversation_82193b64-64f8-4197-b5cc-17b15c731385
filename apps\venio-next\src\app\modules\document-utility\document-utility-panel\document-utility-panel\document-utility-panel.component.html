<div
  class="t-flex t-flex-1 t-justify-between t-items-center t-p-[0.35rem] t-border t-border-l-0 t-border-r-0 t-border-b-1 t-border-t-0 t-border-[#dbdbdb] t-self-start t-px-4">
  <div class="t-flex t-w-full t-error">
    <kendo-multiselect
      [checkboxes]="true"
      [autoClose]="false"
      [clearButton]="false"
      [filterable]="true"
      [data]="filteredList()"
      [ngModel]="selectedUtilityPanels()"
      [tagMapper]="tagMapper"
      (valueChange)="onValueChange($event)"
      (filterChange)="onFilterChange($event)"
      [listHeight]="500"
      class="t-w-full xl:t-w-[390px]">
    </kendo-multiselect>
  </div>
  <div
    class="t-flex-none"
    kendoTooltip
    *ngIf="!isTagPanelPopout && !viewerInFullScreen && !isReviewPanelPopout">
    <button
      kendoButton
      (click)="onFullscreenClick()"
      #parentFullscreenTag
      class="!t-p-[0.3rem]"
      fillMode="clear"
      title="Fullscreen"
      size="none">
      <span
        [parentElement]="parentFullscreenTag.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        [svgUrl]="'assets/svg/icon-review-fullscreen.svg'"
        height="1.1rem"
        width="1.1rem"></span>
    </button>
  </div>
</div>
<div class="t-flex">
  <div
    class="t-flex t-flex-1 t-flex-col t-overflow-hidden t-overflow-y-auto t-h-full">
    <div
      class="t-flex t-flex-col t-w-full t-items-center t-h-[calc(100vh-8rem)]"
      [ngClass]="{ 't-h-full': isTagPanelPopout }">
      <kendo-sortable
        [data]="utilityPanelItems()"
        [kendoSortableBinding]="utilityPanelItems()"
        (dragOver)="onDragOver($event)"
        [navigable]="true"
        [animation]="true"
        (dragStart)="onDragStart($event)"
        class="t-flex t-flex-col t-w-full t-h-full">
        <ng-template let-item="item" let-index="index">
          <kendo-expansionpanel
            [hidden]="!visibilityStatus[item.title]"
            [expanded]="expandedStatus[item.title]"
            (action)="onAction($event, item.title)"
            class="v-review-tags-panel">
            <!-- Custom title template with drag handle and close button -->
            <ng-template kendoExpansionPanelTitleDirective>
              <div class="t-flex t-items-center t-gap-1 t-pl-2">
                <!-- Drag handle -->
                <div class="t-flex">
                  <button
                    kendoButton
                    fillMode="flat"
                    size="none"
                    class="t-p-1 t-cursor-move"
                    (mousedown)="setDragInitiated(true)"
                    (touchstart)="setDragInitiated(true)">
                    <span
                      venioSvgLoader
                      svgUrl="assets/svg/icon-drag-drop.svg"
                      height="1.5rem"
                      width="1rem">
                      <kendo-loader size="small"></kendo-loader>
                    </span>
                  </button>
                </div>
                <!-- Title -->
                <div class="t-flex">
                  <span
                    class="t-font-bold t-whitespace-nowrap v-review-tags-panel-title"
                    >{{ item.title }}</span
                  >
                </div>
                <!-- new notification area moved here-->
                <div
                  #appendNotification
                  class="v-append-notification-container t-inline-block t-text-xs t-ml-3 t-overflow-hidden t-relative t-h-8 t-w-[260px]"></div>
                <button
                  kendoButton
                  *ngIf="item.hasShowHideFields"
                  (click)="panelFilterClick(item.componentId, $event)"
                  [disabled]="isFieldLoading$ | async"
                  fillMode="clear"
                  size="none"
                  class="t-p-1 t-bg-[#F4F4F4] t-cursor-pointer t-pl-3.5 t-rounded !t-h-[24px] t-absolute t-right-[69px] t-leading-none t-overflow-hidden t-group">
                  <span
                    class="t-inline-block t-absolute t-left-0 t-w-3 t-top-0 t-bg-[#6C6C6C] t-h-full"></span>
                  <kendo-loader
                    *ngIf="isFieldLoading$ | async; else tplEyeIcon"
                    themeColor="secondary"
                    size="small"
                    type="pulsing"></kendo-loader>
                  <ng-template #tplEyeIcon>
                    <span
                      class="t-cursor-pointer"
                      venioSvgLoader
                      hoverColor="#FFBB12"
                      color="#979797"
                      [svgUrl]="'assets/svg/icon-show-fields.svg'"
                      height="1rem"
                      width="1rem">
                      <kendo-loader size="small" />
                    </span>
                  </ng-template>
                </button>
                <button
                  kendoButton
                  [ngClass]="
                    selectedUtilityPanels().length <= 1
                      ? ['t-opacity-20', 't-grayscale']
                      : []
                  "
                  (click)="removePanel(item.title, $event)"
                  fillMode="clear"
                  size="none"
                  class="t-p-1 t-cursor-pointer t-absolute t-right-3.5">
                  <span
                    venioSvgLoader
                    svgUrl="assets/svg/icon-close-red-accent-circle.svg"
                    height="1.45rem"
                    width="0.95rem">
                    <kendo-loader size="small"></kendo-loader>
                  </span>
                </button>
              </div>
            </ng-template>

            <!-- Dynamic component loading based on selection -->
            <div
              *ngIf="isComponentLoaded(item) && visibilityStatus[item.title]">
              @if(item.componentId === utilityPanelType.EDAI_AI_PII_EXTRACT ||
              item.componentId === utilityPanelType.EDAI_AI_PII_DETECT){
              <ng-container
                *ngComponentOutlet="
                  getComponent(item) | async;
                  inputs: { panelItem: item }
                "></ng-container>
              } @else {
              <ng-container
                [ngComponentOutlet]="getComponent(item) | async"></ng-container>
              }
            </div>
          </kendo-expansionpanel>
        </ng-template>
      </kendo-sortable>
    </div>
  </div>
</div>

<ng-container
  [ngComponentOutlet]="
    documentUtilityFilterPanelComponent | async
  "></ng-container>
